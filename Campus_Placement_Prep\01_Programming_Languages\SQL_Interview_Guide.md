# SQL Interview Questions & Concepts Guide

## Table of Contents
1. [SQL Fundamentals](#sql-fundamentals)
2. [Database Design](#database-design)
3. [Advanced Queries](#advanced-queries)
4. [Performance Optimization](#performance-optimization)
5. [Common Interview Questions](#common-interview-questions)
6. [Real-World Problem Solving](#real-world-problem-solving)

---

## SQL Fundamentals

### 1. What is SQL and why is it important?

**Answer:**
SQL (Structured Query Language) is a standardized language for managing relational databases.

**Key purposes:**
- **Data Retrieval** - SELECT statements
- **Data Manipulation** - INSERT, UPDATE, DELETE
- **Data Definition** - CREATE, ALTER, DROP
- **Data Control** - GRANT, REVOKE permissions

**Real-world analogy:** SQL is like a universal language for talking to databases - just as English helps people communicate, SQL helps applications communicate with databases.

### 2. Types of SQL Commands

**DDL (Data Definition Language):**
```sql
-- Create table
CREATE TABLE employees (
    id INT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    salary DECIMAL(10,2),
    department_id INT
);

-- Modify table structure
ALTER TABLE employees ADD COLUMN email VARCHAR(100);

-- Delete table
DROP TABLE employees;
```

**DML (Data Manipulation Language):**
```sql
-- Insert data
INSERT INTO employees (id, name, salary, department_id) 
VALUES (1, 'John Doe', 50000.00, 1);

-- Update data
UPDATE employees SET salary = 55000.00 WHERE id = 1;

-- Delete data
DELETE FROM employees WHERE id = 1;
```

**DQL (Data Query Language):**
```sql
-- Retrieve data
SELECT name, salary FROM employees WHERE department_id = 1;
```

**DCL (Data Control Language):**
```sql
-- Grant permissions
GRANT SELECT, INSERT ON employees TO user1;

-- Revoke permissions
REVOKE INSERT ON employees FROM user1;
```

### 3. Primary Key vs Foreign Key

**Primary Key:**
- Uniquely identifies each row
- Cannot be NULL
- Only one per table
- Automatically creates unique index

**Foreign Key:**
- References primary key of another table
- Can be NULL (unless specified otherwise)
- Multiple foreign keys allowed
- Maintains referential integrity

```sql
-- Example schema
CREATE TABLE departments (
    dept_id INT PRIMARY KEY,
    dept_name VARCHAR(50) NOT NULL
);

CREATE TABLE employees (
    emp_id INT PRIMARY KEY,
    emp_name VARCHAR(100) NOT NULL,
    dept_id INT,
    FOREIGN KEY (dept_id) REFERENCES departments(dept_id)
);
```

**Real-world analogy:** Primary key is like a unique ID card number, foreign key is like a reference to your department's ID.

---

## Database Design

### 4. Database Normalization

**Purpose:** Eliminate data redundancy and improve data integrity.

**1st Normal Form (1NF):**
- Each column contains atomic (indivisible) values
- No repeating groups

```sql
-- Violates 1NF (multiple phone numbers in one column)
CREATE TABLE customers_bad (
    id INT,
    name VARCHAR(100),
    phones VARCHAR(200)  -- "************, ************"
);

-- Follows 1NF
CREATE TABLE customers (
    id INT,
    name VARCHAR(100)
);

CREATE TABLE customer_phones (
    customer_id INT,
    phone VARCHAR(15),
    FOREIGN KEY (customer_id) REFERENCES customers(id)
);
```

**2nd Normal Form (2NF):**
- Must be in 1NF
- No partial dependencies (non-key attributes depend on entire primary key)

**3rd Normal Form (3NF):**
- Must be in 2NF
- No transitive dependencies (non-key attributes don't depend on other non-key attributes)

### 5. Types of Relationships

**One-to-One (1:1):**
```sql
-- Each employee has one passport
CREATE TABLE employees (
    emp_id INT PRIMARY KEY,
    name VARCHAR(100)
);

CREATE TABLE passports (
    passport_id INT PRIMARY KEY,
    emp_id INT UNIQUE,
    passport_number VARCHAR(20),
    FOREIGN KEY (emp_id) REFERENCES employees(emp_id)
);
```

**One-to-Many (1:M):**
```sql
-- One department has many employees
CREATE TABLE departments (
    dept_id INT PRIMARY KEY,
    dept_name VARCHAR(50)
);

CREATE TABLE employees (
    emp_id INT PRIMARY KEY,
    name VARCHAR(100),
    dept_id INT,
    FOREIGN KEY (dept_id) REFERENCES departments(dept_id)
);
```

**Many-to-Many (M:M):**
```sql
-- Students can enroll in multiple courses, courses can have multiple students
CREATE TABLE students (
    student_id INT PRIMARY KEY,
    name VARCHAR(100)
);

CREATE TABLE courses (
    course_id INT PRIMARY KEY,
    course_name VARCHAR(100)
);

CREATE TABLE enrollments (
    student_id INT,
    course_id INT,
    enrollment_date DATE,
    PRIMARY KEY (student_id, course_id),
    FOREIGN KEY (student_id) REFERENCES students(student_id),
    FOREIGN KEY (course_id) REFERENCES courses(course_id)
);
```

---

## Advanced Queries

### 6. JOINs Explained

**INNER JOIN:**
```sql
-- Returns only matching records from both tables
SELECT e.name, d.dept_name
FROM employees e
INNER JOIN departments d ON e.dept_id = d.dept_id;
```

**LEFT JOIN:**
```sql
-- Returns all records from left table, matching records from right
SELECT e.name, d.dept_name
FROM employees e
LEFT JOIN departments d ON e.dept_id = d.dept_id;
```

**RIGHT JOIN:**
```sql
-- Returns all records from right table, matching records from left
SELECT e.name, d.dept_name
FROM employees e
RIGHT JOIN departments d ON e.dept_id = d.dept_id;
```

**FULL OUTER JOIN:**
```sql
-- Returns all records when there's a match in either table
SELECT e.name, d.dept_name
FROM employees e
FULL OUTER JOIN departments d ON e.dept_id = d.dept_id;
```

**Real-world analogy:** Think of JOINs like different ways of combining two guest lists for a party - INNER JOIN only invites people on both lists, LEFT JOIN invites everyone from the first list plus matches from the second.

### 7. Aggregate Functions and GROUP BY

```sql
-- Sample data setup
CREATE TABLE sales (
    id INT,
    product VARCHAR(50),
    category VARCHAR(50),
    amount DECIMAL(10,2),
    sale_date DATE
);

-- Basic aggregations
SELECT 
    COUNT(*) as total_sales,
    SUM(amount) as total_revenue,
    AVG(amount) as average_sale,
    MIN(amount) as min_sale,
    MAX(amount) as max_sale
FROM sales;

-- GROUP BY with aggregations
SELECT 
    category,
    COUNT(*) as sales_count,
    SUM(amount) as category_revenue,
    AVG(amount) as avg_sale_amount
FROM sales
GROUP BY category;

-- HAVING clause (filtering groups)
SELECT 
    category,
    COUNT(*) as sales_count,
    SUM(amount) as total_revenue
FROM sales
GROUP BY category
HAVING COUNT(*) > 10 AND SUM(amount) > 1000;
```

### 8. Window Functions

```sql
-- Ranking functions
SELECT 
    name,
    salary,
    department_id,
    ROW_NUMBER() OVER (PARTITION BY department_id ORDER BY salary DESC) as row_num,
    RANK() OVER (PARTITION BY department_id ORDER BY salary DESC) as rank_num,
    DENSE_RANK() OVER (PARTITION BY department_id ORDER BY salary DESC) as dense_rank
FROM employees;

-- Aggregate window functions
SELECT 
    name,
    salary,
    department_id,
    SUM(salary) OVER (PARTITION BY department_id) as dept_total_salary,
    AVG(salary) OVER (PARTITION BY department_id) as dept_avg_salary,
    salary - AVG(salary) OVER (PARTITION BY department_id) as salary_diff_from_avg
FROM employees;

-- Lead and Lag functions
SELECT 
    name,
    salary,
    LAG(salary, 1) OVER (ORDER BY salary) as prev_salary,
    LEAD(salary, 1) OVER (ORDER BY salary) as next_salary
FROM employees;
```

---

## Performance Optimization

### 9. Indexes and Query Optimization

**Types of Indexes:**

```sql
-- Primary index (automatically created with PRIMARY KEY)
CREATE TABLE employees (
    id INT PRIMARY KEY,  -- Clustered index
    name VARCHAR(100)
);

-- Secondary index
CREATE INDEX idx_employee_name ON employees(name);

-- Composite index
CREATE INDEX idx_name_dept ON employees(name, department_id);

-- Unique index
CREATE UNIQUE INDEX idx_employee_email ON employees(email);

-- Partial index (PostgreSQL)
CREATE INDEX idx_active_employees ON employees(name) WHERE status = 'active';
```

**Query Optimization Tips:**

```sql
-- Use EXPLAIN to analyze query execution
EXPLAIN SELECT * FROM employees WHERE department_id = 1;

-- Avoid SELECT *
-- Bad
SELECT * FROM employees;

-- Good
SELECT id, name, salary FROM employees;

-- Use appropriate WHERE clauses
-- Bad (non-sargable)
SELECT * FROM employees WHERE YEAR(hire_date) = 2023;

-- Good (sargable)
SELECT * FROM employees WHERE hire_date >= '2023-01-01' AND hire_date < '2024-01-01';

-- Use EXISTS instead of IN for subqueries
-- Less efficient
SELECT * FROM employees WHERE department_id IN (
    SELECT dept_id FROM departments WHERE location = 'New York'
);

-- More efficient
SELECT * FROM employees e WHERE EXISTS (
    SELECT 1 FROM departments d 
    WHERE d.dept_id = e.department_id AND d.location = 'New York'
);
```

### 10. Transactions and ACID Properties

**ACID Properties:**
- **Atomicity:** All operations in a transaction succeed or fail together
- **Consistency:** Database remains in valid state before and after transaction
- **Isolation:** Concurrent transactions don't interfere with each other
- **Durability:** Committed changes persist even after system failure

```sql
-- Transaction example
BEGIN TRANSACTION;

UPDATE accounts SET balance = balance - 100 WHERE account_id = 1;
UPDATE accounts SET balance = balance + 100 WHERE account_id = 2;

-- Check if both updates were successful
IF @@ERROR = 0
    COMMIT TRANSACTION;
ELSE
    ROLLBACK TRANSACTION;
```

---

## Common Interview Questions

### 11. Find Nth Highest Salary

```sql
-- Method 1: Using LIMIT and OFFSET (MySQL, PostgreSQL)
SELECT DISTINCT salary
FROM employees
ORDER BY salary DESC
LIMIT 1 OFFSET 2;  -- For 3rd highest salary

-- Method 2: Using ROW_NUMBER()
SELECT salary
FROM (
    SELECT salary, ROW_NUMBER() OVER (ORDER BY salary DESC) as rn
    FROM employees
) ranked
WHERE rn = 3;

-- Method 3: Using subquery (works in all databases)
SELECT MAX(salary) as third_highest
FROM employees
WHERE salary < (
    SELECT MAX(salary) FROM employees
    WHERE salary < (SELECT MAX(salary) FROM employees)
);
```

### 12. Find Duplicate Records

```sql
-- Find duplicate names
SELECT name, COUNT(*) as count
FROM employees
GROUP BY name
HAVING COUNT(*) > 1;

-- Find all records with duplicate names
SELECT e1.*
FROM employees e1
INNER JOIN (
    SELECT name
    FROM employees
    GROUP BY name
    HAVING COUNT(*) > 1
) e2 ON e1.name = e2.name;

-- Delete duplicates (keep one record)
DELETE e1 FROM employees e1
INNER JOIN employees e2
WHERE e1.id > e2.id AND e1.name = e2.name;
```

### 13. Self JOIN Example

```sql
-- Find employees and their managers
SELECT 
    e.name as employee_name,
    m.name as manager_name
FROM employees e
LEFT JOIN employees m ON e.manager_id = m.id;

-- Find employees earning more than their manager
SELECT 
    e.name as employee_name,
    e.salary as employee_salary,
    m.name as manager_name,
    m.salary as manager_salary
FROM employees e
INNER JOIN employees m ON e.manager_id = m.id
WHERE e.salary > m.salary;
```

---

## Real-World Problem Solving

### 14. E-commerce Database Design

```sql
-- Users table
CREATE TABLE users (
    user_id INT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Products table
CREATE TABLE products (
    product_id INT PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    stock_quantity INT DEFAULT 0,
    category_id INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Orders table
CREATE TABLE orders (
    order_id INT PRIMARY KEY,
    user_id INT NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id)
);

-- Order items table
CREATE TABLE order_items (
    order_item_id INT PRIMARY KEY,
    order_id INT NOT NULL,
    product_id INT NOT NULL,
    quantity INT NOT NULL,
    unit_price DECIMAL(10,2) NOT NULL,
    FOREIGN KEY (order_id) REFERENCES orders(order_id),
    FOREIGN KEY (product_id) REFERENCES products(product_id)
);

-- Complex query: Top selling products by revenue
SELECT 
    p.name,
    SUM(oi.quantity * oi.unit_price) as total_revenue,
    SUM(oi.quantity) as total_quantity_sold
FROM products p
INNER JOIN order_items oi ON p.product_id = oi.product_id
INNER JOIN orders o ON oi.order_id = o.order_id
WHERE o.status = 'completed'
GROUP BY p.product_id, p.name
ORDER BY total_revenue DESC
LIMIT 10;
```

### 15. Data Analysis Queries

```sql
-- Monthly sales report
SELECT 
    YEAR(o.created_at) as year,
    MONTH(o.created_at) as month,
    COUNT(DISTINCT o.order_id) as total_orders,
    COUNT(DISTINCT o.user_id) as unique_customers,
    SUM(o.total_amount) as total_revenue,
    AVG(o.total_amount) as avg_order_value
FROM orders o
WHERE o.status = 'completed'
GROUP BY YEAR(o.created_at), MONTH(o.created_at)
ORDER BY year DESC, month DESC;

-- Customer lifetime value
SELECT 
    u.user_id,
    u.username,
    COUNT(o.order_id) as total_orders,
    SUM(o.total_amount) as lifetime_value,
    AVG(o.total_amount) as avg_order_value,
    MIN(o.created_at) as first_order_date,
    MAX(o.created_at) as last_order_date
FROM users u
LEFT JOIN orders o ON u.user_id = o.user_id AND o.status = 'completed'
GROUP BY u.user_id, u.username
HAVING COUNT(o.order_id) > 0
ORDER BY lifetime_value DESC;

-- Product performance analysis
SELECT 
    p.name,
    p.price,
    p.stock_quantity,
    COALESCE(sales.total_sold, 0) as total_sold,
    COALESCE(sales.revenue, 0) as revenue,
    CASE 
        WHEN sales.total_sold IS NULL THEN 'No Sales'
        WHEN sales.total_sold < 10 THEN 'Low Sales'
        WHEN sales.total_sold < 50 THEN 'Medium Sales'
        ELSE 'High Sales'
    END as sales_category
FROM products p
LEFT JOIN (
    SELECT 
        oi.product_id,
        SUM(oi.quantity) as total_sold,
        SUM(oi.quantity * oi.unit_price) as revenue
    FROM order_items oi
    INNER JOIN orders o ON oi.order_id = o.order_id
    WHERE o.status = 'completed'
    GROUP BY oi.product_id
) sales ON p.product_id = sales.product_id
ORDER BY revenue DESC NULLS LAST;
```

### Key Interview Tips:
1. **Understand the business context** behind queries
2. **Explain your thought process** step by step
3. **Consider performance implications** of your queries
4. **Handle edge cases** (NULL values, empty results)
5. **Know when to use different types of JOINs**
6. **Understand indexing strategies**

### Practice Problems:
1. Design a social media database schema
2. Write queries for a library management system
3. Optimize slow-running queries
4. Implement data warehousing concepts
5. Create stored procedures and triggers

### SQL Best Practices:
- Use meaningful table and column names
- Always use proper JOINs instead of WHERE clause joins
- Index frequently queried columns
- Avoid SELECT * in production code
- Use parameterized queries to prevent SQL injection
- Comment complex queries
- Test queries with sample data

Remember: **SQL is about thinking in sets, not loops - focus on what data you want, not how to get it step by step!**
