# Object-Oriented Programming (OOP) Concepts Interview Guide

## Table of Contents
1. [OOP Fundamentals](#oop-fundamentals)
2. [Four Pillars of OOP](#four-pillars-of-oop)
3. [Advanced OOP Concepts](#advanced-oop-concepts)
4. [Design Patterns](#design-patterns)
5. [Common Interview Questions](#common-interview-questions)
6. [Real-World Applications](#real-world-applications)

---

## OOP Fundamentals

### 1. What is Object-Oriented Programming?

**Answer:**
OOP is a programming paradigm based on the concept of "objects" that contain data (attributes) and code (methods).

**Key Concepts:**
- **Class** - Blueprint or template for creating objects
- **Object** - Instance of a class
- **Attribute** - Data stored in an object
- **Method** - Function that belongs to an object

**Real-world analogy:** Think of a class as a blueprint for a house, and objects as actual houses built from that blueprint.

```python
# Class definition
class Car:
    # Class variable (shared by all instances)
    wheels = 4
    
    # Constructor method
    def __init__(self, brand, model, year):
        # Instance variables (unique to each object)
        self.brand = brand
        self.model = model
        self.year = year
        self.speed = 0
    
    # Instance method
    def accelerate(self, increment):
        self.speed += increment
        return f"{self.brand} {self.model} is now going {self.speed} mph"
    
    def brake(self, decrement):
        self.speed = max(0, self.speed - decrement)
        return f"{self.brand} {self.model} slowed down to {self.speed} mph"
    
    # String representation
    def __str__(self):
        return f"{self.year} {self.brand} {self.model}"

# Creating objects (instances)
car1 = Car("Toyota", "Camry", 2022)
car2 = Car("Honda", "Civic", 2021)

print(car1.accelerate(30))  # Toyota Camry is now going 30 mph
print(car2.accelerate(25))  # Honda Civic is now going 25 mph
```

---

## Four Pillars of OOP

### 2. Encapsulation

**Definition:** Bundling data and methods together while hiding internal implementation details.

**Benefits:**
- Data protection
- Code maintainability
- Controlled access to object state

```python
class BankAccount:
    def __init__(self, account_number, initial_balance=0):
        self.account_number = account_number
        self.__balance = initial_balance  # Private attribute (name mangling)
        self._transaction_history = []    # Protected attribute (convention)
    
    # Public method to access private data
    def get_balance(self):
        return self.__balance
    
    # Public method to modify private data with validation
    def deposit(self, amount):
        if amount > 0:
            self.__balance += amount
            self._transaction_history.append(f"Deposited: ${amount}")
            return True
        return False
    
    def withdraw(self, amount):
        if 0 < amount <= self.__balance:
            self.__balance -= amount
            self._transaction_history.append(f"Withdrew: ${amount}")
            return True
        return False
    
    # Property decorator for controlled access
    @property
    def balance(self):
        return self.__balance
    
    @balance.setter
    def balance(self, value):
        if value >= 0:
            self.__balance = value
        else:
            raise ValueError("Balance cannot be negative")

# Usage
account = BankAccount("12345", 1000)
print(account.get_balance())  # 1000
account.deposit(500)
print(account.balance)        # 1500
# account.__balance           # AttributeError: can't access private attribute
```

**Real-world analogy:** Encapsulation is like a car's dashboard - you can see the speedometer and use the steering wheel, but you can't directly access the engine internals.

### 3. Inheritance

**Definition:** Creating new classes based on existing classes, inheriting their attributes and methods.

```python
# Base class (Parent class)
class Animal:
    def __init__(self, name, species):
        self.name = name
        self.species = species
        self.is_alive = True
    
    def eat(self):
        return f"{self.name} is eating"
    
    def sleep(self):
        return f"{self.name} is sleeping"
    
    def make_sound(self):
        return "Some generic animal sound"

# Derived class (Child class)
class Dog(Animal):
    def __init__(self, name, breed):
        super().__init__(name, "Canine")  # Call parent constructor
        self.breed = breed
    
    # Method overriding
    def make_sound(self):
        return f"{self.name} says Woof!"
    
    # New method specific to Dog
    def fetch(self):
        return f"{self.name} is fetching the ball"

class Cat(Animal):
    def __init__(self, name, breed):
        super().__init__(name, "Feline")
        self.breed = breed
    
    def make_sound(self):
        return f"{self.name} says Meow!"
    
    def climb(self):
        return f"{self.name} is climbing a tree"

# Multiple inheritance
class FlyingAnimal:
    def fly(self):
        return "Flying high in the sky"

class Bird(Animal, FlyingAnimal):
    def __init__(self, name, wing_span):
        super().__init__(name, "Avian")
        self.wing_span = wing_span
    
    def make_sound(self):
        return f"{self.name} is chirping"

# Usage
dog = Dog("Buddy", "Golden Retriever")
cat = Cat("Whiskers", "Persian")
bird = Bird("Tweety", "6 inches")

print(dog.make_sound())  # Buddy says Woof!
print(cat.make_sound())  # Whiskers says Meow!
print(bird.fly())        # Flying high in the sky
```

**Types of Inheritance:**
- **Single Inheritance** - One parent class
- **Multiple Inheritance** - Multiple parent classes
- **Multilevel Inheritance** - Chain of inheritance
- **Hierarchical Inheritance** - Multiple children from one parent

### 4. Polymorphism

**Definition:** Same interface, different implementations. Objects of different types can be treated as instances of the same type.

```python
# Runtime polymorphism (Method overriding)
class Shape:
    def area(self):
        raise NotImplementedError("Subclass must implement area method")
    
    def perimeter(self):
        raise NotImplementedError("Subclass must implement perimeter method")

class Rectangle(Shape):
    def __init__(self, width, height):
        self.width = width
        self.height = height
    
    def area(self):
        return self.width * self.height
    
    def perimeter(self):
        return 2 * (self.width + self.height)

class Circle(Shape):
    def __init__(self, radius):
        self.radius = radius
    
    def area(self):
        return 3.14159 * self.radius ** 2
    
    def perimeter(self):
        return 2 * 3.14159 * self.radius

# Polymorphic function
def print_shape_info(shape):
    print(f"Area: {shape.area()}")
    print(f"Perimeter: {shape.perimeter()}")

# Usage - same function works with different object types
shapes = [
    Rectangle(5, 3),
    Circle(4),
    Rectangle(2, 8)
]

for shape in shapes:
    print_shape_info(shape)  # Polymorphic behavior

# Compile-time polymorphism (Method overloading - simulated in Python)
class Calculator:
    def add(self, *args):
        if len(args) == 2:
            return args[0] + args[1]
        elif len(args) == 3:
            return args[0] + args[1] + args[2]
        else:
            return sum(args)

calc = Calculator()
print(calc.add(2, 3))        # 5
print(calc.add(1, 2, 3))     # 6
print(calc.add(1, 2, 3, 4))  # 10
```

### 5. Abstraction

**Definition:** Hiding complex implementation details while showing only essential features.

```python
from abc import ABC, abstractmethod

# Abstract base class
class Vehicle(ABC):
    def __init__(self, brand, model):
        self.brand = brand
        self.model = model
    
    @abstractmethod
    def start_engine(self):
        pass
    
    @abstractmethod
    def stop_engine(self):
        pass
    
    # Concrete method (shared implementation)
    def get_info(self):
        return f"{self.brand} {self.model}"

class Car(Vehicle):
    def start_engine(self):
        return "Car engine started with key"
    
    def stop_engine(self):
        return "Car engine stopped"

class Motorcycle(Vehicle):
    def start_engine(self):
        return "Motorcycle engine started with kick/button"
    
    def stop_engine(self):
        return "Motorcycle engine stopped"

# Cannot instantiate abstract class
# vehicle = Vehicle("Generic", "Model")  # TypeError

# Can instantiate concrete classes
car = Car("Toyota", "Camry")
bike = Motorcycle("Harley", "Davidson")

print(car.start_engine())   # Car engine started with key
print(bike.start_engine())  # Motorcycle engine started with kick/button
```

---

## Advanced OOP Concepts

### 6. Composition vs Inheritance

**Composition:** "Has-a" relationship - objects contain other objects.

```python
# Composition example
class Engine:
    def __init__(self, horsepower):
        self.horsepower = horsepower
    
    def start(self):
        return "Engine started"
    
    def stop(self):
        return "Engine stopped"

class Wheel:
    def __init__(self, size):
        self.size = size
    
    def rotate(self):
        return f"{self.size}-inch wheel rotating"

class Car:
    def __init__(self, brand, model, engine_hp):
        self.brand = brand
        self.model = model
        self.engine = Engine(engine_hp)  # Composition
        self.wheels = [Wheel(17) for _ in range(4)]  # Composition
    
    def start(self):
        return f"{self.brand} {self.model}: {self.engine.start()}"
    
    def drive(self):
        wheel_status = [wheel.rotate() for wheel in self.wheels]
        return f"Driving with {len(self.wheels)} wheels"

# Usage
car = Car("Toyota", "Camry", 200)
print(car.start())  # Toyota Camry: Engine started
```

**When to use:**
- **Inheritance** - "Is-a" relationship (Dog is an Animal)
- **Composition** - "Has-a" relationship (Car has an Engine)

### 7. Method Resolution Order (MRO)

```python
class A:
    def method(self):
        print("A method")

class B(A):
    def method(self):
        print("B method")
        super().method()

class C(A):
    def method(self):
        print("C method")
        super().method()

class D(B, C):
    def method(self):
        print("D method")
        super().method()

# Check MRO
print(D.__mro__)
# (<class '__main__.D'>, <class '__main__.B'>, <class '__main__.C'>, <class '__main__.A'>, <class 'object'>)

d = D()
d.method()
# Output:
# D method
# B method
# C method
# A method
```

---

## Design Patterns

### 8. Common Design Patterns

**Singleton Pattern:**
```python
class Singleton:
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not hasattr(self, 'initialized'):
            self.value = 0
            self.initialized = True

# Usage
s1 = Singleton()
s2 = Singleton()
print(s1 is s2)  # True - same instance
```

**Factory Pattern:**
```python
class Animal:
    def make_sound(self):
        pass

class Dog(Animal):
    def make_sound(self):
        return "Woof!"

class Cat(Animal):
    def make_sound(self):
        return "Meow!"

class AnimalFactory:
    @staticmethod
    def create_animal(animal_type):
        if animal_type.lower() == "dog":
            return Dog()
        elif animal_type.lower() == "cat":
            return Cat()
        else:
            raise ValueError("Unknown animal type")

# Usage
factory = AnimalFactory()
dog = factory.create_animal("dog")
cat = factory.create_animal("cat")
```

**Observer Pattern:**
```python
class Subject:
    def __init__(self):
        self._observers = []
        self._state = None
    
    def attach(self, observer):
        self._observers.append(observer)
    
    def detach(self, observer):
        self._observers.remove(observer)
    
    def notify(self):
        for observer in self._observers:
            observer.update(self._state)
    
    def set_state(self, state):
        self._state = state
        self.notify()

class Observer:
    def __init__(self, name):
        self.name = name
    
    def update(self, state):
        print(f"{self.name} received update: {state}")

# Usage
subject = Subject()
observer1 = Observer("Observer 1")
observer2 = Observer("Observer 2")

subject.attach(observer1)
subject.attach(observer2)
subject.set_state("New State")  # Both observers get notified
```

---

## Common Interview Questions

### 9. What is the difference between Abstract Class and Interface?

**Abstract Class:**
- Can have both abstract and concrete methods
- Can have instance variables
- Supports single inheritance
- Can have constructors

**Interface (Protocol in Python):**
- All methods are abstract (by convention)
- No instance variables (only constants)
- Supports multiple inheritance
- No constructors

```python
# Abstract Class
from abc import ABC, abstractmethod

class AbstractShape(ABC):
    def __init__(self, color):
        self.color = color  # Concrete attribute
    
    @abstractmethod
    def area(self):
        pass
    
    def get_color(self):  # Concrete method
        return self.color

# Interface (Protocol)
from typing import Protocol

class Drawable(Protocol):
    def draw(self) -> None:
        ...
    
    def get_bounds(self) -> tuple:
        ...

class Rectangle(AbstractShape):
    def __init__(self, width, height, color):
        super().__init__(color)
        self.width = width
        self.height = height
    
    def area(self):
        return self.width * self.height
    
    def draw(self):
        print(f"Drawing {self.color} rectangle")
    
    def get_bounds(self):
        return (self.width, self.height)
```

### 10. Explain Method Overloading vs Method Overriding

**Method Overloading:**
- Same method name, different parameters
- Compile-time polymorphism
- Within the same class

**Method Overriding:**
- Same method signature in parent and child class
- Runtime polymorphism
- Inheritance relationship required

```python
# Method Overriding
class Animal:
    def make_sound(self):
        return "Generic animal sound"

class Dog(Animal):
    def make_sound(self):  # Overriding parent method
        return "Woof!"

# Method Overloading (simulated in Python)
class MathOperations:
    def add(self, a, b=None, c=None):
        if b is None:
            return a
        elif c is None:
            return a + b
        else:
            return a + b + c

math_ops = MathOperations()
print(math_ops.add(5))        # 5
print(math_ops.add(5, 3))     # 8
print(math_ops.add(5, 3, 2))  # 10
```

---

## Real-World Applications

### 11. Library Management System

```python
from datetime import datetime, timedelta
from typing import List

class Book:
    def __init__(self, isbn, title, author, copies=1):
        self.isbn = isbn
        self.title = title
        self.author = author
        self.total_copies = copies
        self.available_copies = copies
    
    def __str__(self):
        return f"{self.title} by {self.author}"

class Member:
    def __init__(self, member_id, name, email):
        self.member_id = member_id
        self.name = name
        self.email = email
        self.borrowed_books: List[Book] = []
        self.borrow_history = []
    
    def can_borrow(self):
        return len(self.borrowed_books) < 3  # Max 3 books

class Transaction:
    def __init__(self, member, book, transaction_type):
        self.member = member
        self.book = book
        self.transaction_type = transaction_type  # 'borrow' or 'return'
        self.date = datetime.now()
        self.due_date = self.date + timedelta(days=14) if transaction_type == 'borrow' else None

class Library:
    def __init__(self):
        self.books = {}  # isbn -> Book
        self.members = {}  # member_id -> Member
        self.transactions = []
    
    def add_book(self, book):
        if book.isbn in self.books:
            self.books[book.isbn].total_copies += book.total_copies
            self.books[book.isbn].available_copies += book.available_copies
        else:
            self.books[book.isbn] = book
    
    def register_member(self, member):
        self.members[member.member_id] = member
    
    def borrow_book(self, member_id, isbn):
        if member_id not in self.members:
            return "Member not found"
        
        if isbn not in self.books:
            return "Book not found"
        
        member = self.members[member_id]
        book = self.books[isbn]
        
        if not member.can_borrow():
            return "Member has reached borrowing limit"
        
        if book.available_copies <= 0:
            return "Book not available"
        
        # Process borrowing
        book.available_copies -= 1
        member.borrowed_books.append(book)
        transaction = Transaction(member, book, 'borrow')
        self.transactions.append(transaction)
        member.borrow_history.append(transaction)
        
        return f"Book '{book.title}' borrowed successfully. Due date: {transaction.due_date.date()}"
    
    def return_book(self, member_id, isbn):
        if member_id not in self.members:
            return "Member not found"
        
        member = self.members[member_id]
        book_to_return = None
        
        for book in member.borrowed_books:
            if book.isbn == isbn:
                book_to_return = book
                break
        
        if not book_to_return:
            return "Book not borrowed by this member"
        
        # Process return
        book_to_return.available_copies += 1
        member.borrowed_books.remove(book_to_return)
        transaction = Transaction(member, book_to_return, 'return')
        self.transactions.append(transaction)
        
        return f"Book '{book_to_return.title}' returned successfully"

# Usage example
library = Library()

# Add books
book1 = Book("978-0134685991", "Effective Java", "Joshua Bloch", 3)
book2 = Book("978-0135166307", "Clean Code", "Robert Martin", 2)
library.add_book(book1)
library.add_book(book2)

# Register members
member1 = Member("M001", "John Doe", "<EMAIL>")
member2 = Member("M002", "Jane Smith", "<EMAIL>")
library.register_member(member1)
library.register_member(member2)

# Borrow and return books
print(library.borrow_book("M001", "978-0134685991"))
print(library.return_book("M001", "978-0134685991"))
```

### Key Interview Tips:
1. **Understand the relationships** between classes
2. **Know when to use inheritance vs composition**
3. **Be familiar with common design patterns**
4. **Practice implementing real-world scenarios**
5. **Understand the trade-offs** of different OOP approaches
6. **Know the SOLID principles**

### SOLID Principles:
- **S**ingle Responsibility Principle
- **O**pen/Closed Principle
- **L**iskov Substitution Principle
- **I**nterface Segregation Principle
- **D**ependency Inversion Principle

### Practice Problems:
1. Design a parking lot system
2. Implement a chess game
3. Create a hotel booking system
4. Design a social media platform
5. Build a file system hierarchy

Remember: **OOP is about modeling real-world entities and their relationships - think in terms of objects and their interactions!**
