# Django Interview Guide

## Table of Contents
1. [Django Fundamentals](#django-fundamentals)
2. [Models and Database](#models-and-database)
3. [Views and URLs](#views-and-urls)
4. [Templates](#templates)
5. [Forms and Validation](#forms-and-validation)
6. [Authentication and Security](#authentication-and-security)
7. [Common Interview Questions](#common-interview-questions)

---

## Django Fundamentals

### 1. What is Django and its architecture?

**Answer:**
Django is a high-level Python web framework that follows the Model-View-Template (MVT) pattern.

**MVT Architecture:**
- **Model** - Data layer (database interactions)
- **View** - Business logic layer (processes requests)
- **Template** - Presentation layer (HTML with Django template language)

**Real-world analogy:** Django is like a restaurant - Models are the kitchen inventory, Views are the chefs who prepare dishes, Templates are the presentation/plating.

```python
# Project structure
myproject/
    manage.py
    myproject/
        __init__.py
        settings.py
        urls.py
        wsgi.py
    myapp/
        __init__.py
        admin.py
        apps.py
        models.py
        views.py
        urls.py
        migrations/
        templates/
        static/
```

### 2. Django Settings and Configuration

```python
# settings.py
import os
from pathlib import Path

BASE_DIR = Path(__file__).resolve().parent.parent

# Security settings
SECRET_KEY = os.environ.get('SECRET_KEY', 'your-secret-key')
DEBUG = os.environ.get('DEBUG', 'False').lower() == 'true'
ALLOWED_HOSTS = ['localhost', '127.0.0.1', 'yourdomain.com']

# Application definition
INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'myapp',  # Custom app
    'rest_framework',  # Third-party app
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

# Database configuration
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': os.environ.get('DB_NAME', 'myproject'),
        'USER': os.environ.get('DB_USER', 'postgres'),
        'PASSWORD': os.environ.get('DB_PASSWORD', ''),
        'HOST': os.environ.get('DB_HOST', 'localhost'),
        'PORT': os.environ.get('DB_PORT', '5432'),
    }
}

# Static files
STATIC_URL = '/static/'
STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')
STATICFILES_DIRS = [os.path.join(BASE_DIR, 'static')]

# Media files
MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')
```

---

## Models and Database

### 3. Django Models

```python
# models.py
from django.db import models
from django.contrib.auth.models import User
from django.urls import reverse

class Category(models.Model):
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        verbose_name_plural = "Categories"
        ordering = ['name']
    
    def __str__(self):
        return self.name

class Post(models.Model):
    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('published', 'Published'),
        ('archived', 'Archived'),
    ]
    
    title = models.CharField(max_length=200)
    slug = models.SlugField(unique=True)
    author = models.ForeignKey(User, on_delete=models.CASCADE, related_name='posts')
    category = models.ForeignKey(Category, on_delete=models.SET_NULL, null=True)
    content = models.TextField()
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default='draft')
    tags = models.ManyToManyField('Tag', blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    published_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['status', 'created_at']),
        ]
    
    def __str__(self):
        return self.title
    
    def get_absolute_url(self):
        return reverse('post_detail', kwargs={'slug': self.slug})
    
    @property
    def is_published(self):
        return self.status == 'published'

class Tag(models.Model):
    name = models.CharField(max_length=50, unique=True)
    
    def __str__(self):
        return self.name

class Comment(models.Model):
    post = models.ForeignKey(Post, on_delete=models.CASCADE, related_name='comments')
    author = models.ForeignKey(User, on_delete=models.CASCADE)
    content = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    is_approved = models.BooleanField(default=False)
    
    class Meta:
        ordering = ['created_at']
    
    def __str__(self):
        return f'Comment by {self.author.username} on {self.post.title}'
```

### 4. Database Operations and QuerySets

```python
# views.py or shell examples
from django.db.models import Q, Count, Avg
from myapp.models import Post, Category, Comment

# Basic queries
posts = Post.objects.all()
published_posts = Post.objects.filter(status='published')
recent_posts = Post.objects.filter(created_at__gte=timezone.now() - timedelta(days=7))

# Complex queries
posts_with_comments = Post.objects.filter(comments__isnull=False).distinct()
popular_posts = Post.objects.annotate(
    comment_count=Count('comments')
).filter(comment_count__gte=5)

# Q objects for complex lookups
search_posts = Post.objects.filter(
    Q(title__icontains='django') | Q(content__icontains='django')
)

# Aggregation
stats = Post.objects.aggregate(
    total_posts=Count('id'),
    avg_comments=Avg('comments__id')
)

# Prefetch related data to avoid N+1 queries
posts_with_data = Post.objects.select_related('author', 'category').prefetch_related('tags', 'comments')

# Custom managers
class PublishedPostManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset().filter(status='published')

class Post(models.Model):
    # ... fields ...
    objects = models.Manager()  # Default manager
    published = PublishedPostManager()  # Custom manager
    
    # Usage: Post.published.all()
```

---

## Views and URLs

### 5. Function-Based Views

```python
# views.py
from django.shortcuts import render, get_object_or_404, redirect
from django.http import HttpResponse, JsonResponse
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.paginator import Paginator
from .models import Post, Category
from .forms import PostForm

def post_list(request):
    posts = Post.published.all()
    category_id = request.GET.get('category')
    
    if category_id:
        posts = posts.filter(category_id=category_id)
    
    # Pagination
    paginator = Paginator(posts, 10)  # 10 posts per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    categories = Category.objects.all()
    
    context = {
        'page_obj': page_obj,
        'categories': categories,
        'selected_category': category_id
    }
    return render(request, 'blog/post_list.html', context)

def post_detail(request, slug):
    post = get_object_or_404(Post, slug=slug, status='published')
    comments = post.comments.filter(is_approved=True)
    
    context = {
        'post': post,
        'comments': comments
    }
    return render(request, 'blog/post_detail.html', context)

@login_required
def post_create(request):
    if request.method == 'POST':
        form = PostForm(request.POST)
        if form.is_valid():
            post = form.save(commit=False)
            post.author = request.user
            post.save()
            form.save_m2m()  # Save many-to-many relationships
            messages.success(request, 'Post created successfully!')
            return redirect('post_detail', slug=post.slug)
    else:
        form = PostForm()
    
    return render(request, 'blog/post_form.html', {'form': form})

def api_posts(request):
    posts = Post.published.values('title', 'slug', 'created_at')
    return JsonResponse(list(posts), safe=False)
```

### 6. Class-Based Views

```python
# views.py
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView
from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin
from django.urls import reverse_lazy

class PostListView(ListView):
    model = Post
    template_name = 'blog/post_list.html'
    context_object_name = 'posts'
    paginate_by = 10
    
    def get_queryset(self):
        queryset = Post.published.select_related('author', 'category')
        category_id = self.request.GET.get('category')
        if category_id:
            queryset = queryset.filter(category_id=category_id)
        return queryset
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['categories'] = Category.objects.all()
        return context

class PostDetailView(DetailView):
    model = Post
    template_name = 'blog/post_detail.html'
    context_object_name = 'post'
    
    def get_queryset(self):
        return Post.published.select_related('author', 'category').prefetch_related('tags')

class PostCreateView(LoginRequiredMixin, CreateView):
    model = Post
    form_class = PostForm
    template_name = 'blog/post_form.html'
    
    def form_valid(self, form):
        form.instance.author = self.request.user
        return super().form_valid(form)

class PostUpdateView(LoginRequiredMixin, UserPassesTestMixin, UpdateView):
    model = Post
    form_class = PostForm
    template_name = 'blog/post_form.html'
    
    def test_func(self):
        post = self.get_object()
        return self.request.user == post.author

class PostDeleteView(LoginRequiredMixin, UserPassesTestMixin, DeleteView):
    model = Post
    template_name = 'blog/post_confirm_delete.html'
    success_url = reverse_lazy('post_list')
    
    def test_func(self):
        post = self.get_object()
        return self.request.user == post.author
```

### 7. URL Configuration

```python
# myapp/urls.py
from django.urls import path
from . import views

app_name = 'blog'

urlpatterns = [
    path('', views.PostListView.as_view(), name='post_list'),
    path('post/<slug:slug>/', views.PostDetailView.as_view(), name='post_detail'),
    path('post/new/', views.PostCreateView.as_view(), name='post_create'),
    path('post/<slug:slug>/edit/', views.PostUpdateView.as_view(), name='post_update'),
    path('post/<slug:slug>/delete/', views.PostDeleteView.as_view(), name='post_delete'),
    path('api/posts/', views.api_posts, name='api_posts'),
]

# myproject/urls.py
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static

urlpatterns = [
    path('admin/', admin.site.urls),
    path('', include('blog.urls')),
    path('accounts/', include('django.contrib.auth.urls')),
]

if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
```

---

## Templates

### 8. Django Template Language

```html
<!-- base.html -->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}My Blog{% endblock %}</title>
    {% load static %}
    <link rel="stylesheet" href="{% static 'css/style.css' %}">
</head>
<body>
    <nav class="navbar">
        <a href="{% url 'blog:post_list' %}">Home</a>
        {% if user.is_authenticated %}
            <a href="{% url 'blog:post_create' %}">New Post</a>
            <a href="{% url 'logout' %}">Logout ({{ user.username }})</a>
        {% else %}
            <a href="{% url 'login' %}">Login</a>
        {% endif %}
    </nav>
    
    <main>
        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }}">
                    {{ message }}
                </div>
            {% endfor %}
        {% endif %}
        
        {% block content %}
        {% endblock %}
    </main>
    
    <footer>
        <p>&copy; 2024 My Blog</p>
    </footer>
</body>
</html>

<!-- post_list.html -->
{% extends 'base.html' %}

{% block title %}Posts - {{ block.super }}{% endblock %}

{% block content %}
<h1>Blog Posts</h1>

<!-- Category filter -->
<div class="filters">
    <a href="{% url 'blog:post_list' %}">All</a>
    {% for category in categories %}
        <a href="{% url 'blog:post_list' %}?category={{ category.id }}">
            {{ category.name }}
        </a>
    {% endfor %}
</div>

<!-- Posts -->
{% for post in page_obj %}
    <article class="post-preview">
        <h2><a href="{{ post.get_absolute_url }}">{{ post.title }}</a></h2>
        <p class="meta">
            By {{ post.author.username }} on {{ post.created_at|date:"F d, Y" }}
            in {{ post.category.name }}
        </p>
        <p>{{ post.content|truncatewords:30 }}</p>
        <div class="tags">
            {% for tag in post.tags.all %}
                <span class="tag">{{ tag.name }}</span>
            {% endfor %}
        </div>
    </article>
{% empty %}
    <p>No posts found.</p>
{% endfor %}

<!-- Pagination -->
{% if page_obj.has_other_pages %}
    <div class="pagination">
        {% if page_obj.has_previous %}
            <a href="?page=1">&laquo; first</a>
            <a href="?page={{ page_obj.previous_page_number }}">previous</a>
        {% endif %}
        
        <span class="current">
            Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
        </span>
        
        {% if page_obj.has_next %}
            <a href="?page={{ page_obj.next_page_number }}">next</a>
            <a href="?page={{ page_obj.paginator.num_pages }}">last &raquo;</a>
        {% endif %}
    </div>
{% endif %}
{% endblock %}
```

---

## Forms and Validation

### 9. Django Forms

```python
# forms.py
from django import forms
from django.contrib.auth.forms import UserCreationForm
from django.contrib.auth.models import User
from .models import Post, Comment

class PostForm(forms.ModelForm):
    class Meta:
        model = Post
        fields = ['title', 'slug', 'category', 'content', 'tags', 'status']
        widgets = {
            'title': forms.TextInput(attrs={'class': 'form-control'}),
            'slug': forms.TextInput(attrs={'class': 'form-control'}),
            'content': forms.Textarea(attrs={'class': 'form-control', 'rows': 10}),
            'category': forms.Select(attrs={'class': 'form-control'}),
            'tags': forms.CheckboxSelectMultiple(),
            'status': forms.Select(attrs={'class': 'form-control'}),
        }
    
    def clean_slug(self):
        slug = self.cleaned_data['slug']
        if Post.objects.filter(slug=slug).exclude(pk=self.instance.pk).exists():
            raise forms.ValidationError('A post with this slug already exists.')
        return slug
    
    def clean_title(self):
        title = self.cleaned_data['title']
        if len(title) < 5:
            raise forms.ValidationError('Title must be at least 5 characters long.')
        return title

class CommentForm(forms.ModelForm):
    class Meta:
        model = Comment
        fields = ['content']
        widgets = {
            'content': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': 'Write your comment...'
            })
        }

class CustomUserCreationForm(UserCreationForm):
    email = forms.EmailField(required=True)
    first_name = forms.CharField(max_length=30, required=True)
    last_name = forms.CharField(max_length=30, required=True)
    
    class Meta:
        model = User
        fields = ('username', 'first_name', 'last_name', 'email', 'password1', 'password2')
    
    def save(self, commit=True):
        user = super().save(commit=False)
        user.email = self.cleaned_data['email']
        user.first_name = self.cleaned_data['first_name']
        user.last_name = self.cleaned_data['last_name']
        if commit:
            user.save()
        return user

# Custom validators
def validate_file_size(value):
    filesize = value.size
    if filesize > 5 * 1024 * 1024:  # 5MB
        raise forms.ValidationError("File size cannot exceed 5MB")

class ProfileForm(forms.Form):
    avatar = forms.ImageField(validators=[validate_file_size])
    bio = forms.CharField(widget=forms.Textarea, max_length=500)
```

---

## Authentication and Security

### 10. User Authentication

```python
# views.py
from django.contrib.auth import login, authenticate
from django.contrib.auth.decorators import login_required
from django.contrib.auth.views import LoginView, LogoutView
from django.contrib.auth.forms import AuthenticationForm

class CustomLoginView(LoginView):
    template_name = 'registration/login.html'
    form_class = AuthenticationForm
    
    def get_success_url(self):
        return reverse_lazy('blog:post_list')

def register(request):
    if request.method == 'POST':
        form = CustomUserCreationForm(request.POST)
        if form.is_valid():
            user = form.save()
            username = form.cleaned_data.get('username')
            messages.success(request, f'Account created for {username}!')
            login(request, user)
            return redirect('blog:post_list')
    else:
        form = CustomUserCreationForm()
    return render(request, 'registration/register.html', {'form': form})

@login_required
def profile(request):
    if request.method == 'POST':
        form = ProfileForm(request.POST, request.FILES)
        if form.is_valid():
            # Process form data
            pass
    else:
        form = ProfileForm()
    return render(request, 'accounts/profile.html', {'form': form})

# Custom decorators
from functools import wraps

def staff_required(view_func):
    @wraps(view_func)
    def _wrapped_view(request, *args, **kwargs):
        if not request.user.is_staff:
            return HttpResponseForbidden("Staff access required")
        return view_func(request, *args, **kwargs)
    return _wrapped_view
```

---

## Common Interview Questions

### 11. What is Django ORM and its advantages?

**Answer:**
Django ORM (Object-Relational Mapping) is a layer that allows you to interact with databases using Python objects instead of SQL.

**Advantages:**
- **Database abstraction** - Works with multiple database backends
- **Security** - Prevents SQL injection attacks
- **Productivity** - Faster development with Python syntax
- **Migrations** - Automatic schema management
- **Relationships** - Easy handling of foreign keys and many-to-many

```python
# ORM vs Raw SQL
# ORM way
users = User.objects.filter(is_active=True).order_by('username')

# Raw SQL equivalent
users = User.objects.raw(
    "SELECT * FROM auth_user WHERE is_active = %s ORDER BY username",
    [True]
)
```

### 12. Explain Django's request-response cycle

**Answer:**
1. **URL Resolution** - Django matches the URL to a view
2. **Middleware Processing** - Request passes through middleware
3. **View Execution** - View processes the request
4. **Template Rendering** - Template is rendered with context
5. **Response Creation** - HTTP response is created
6. **Middleware Processing** - Response passes through middleware
7. **Response Sent** - Final response sent to client

### Key Interview Tips:
1. **Understand MVT pattern** thoroughly
2. **Know Django's security features** (CSRF, XSS protection)
3. **Be familiar with Django REST Framework** for APIs
4. **Understand database optimization** (select_related, prefetch_related)
5. **Know deployment considerations** (static files, environment variables)
6. **Understand Django's admin interface** customization

### Practice Projects:
1. Build a blog with user authentication
2. Create an e-commerce platform
3. Develop a REST API with Django REST Framework
4. Build a social media application
5. Create a task management system

Remember: **Django's philosophy is "Don't Repeat Yourself" (DRY) - leverage Django's built-in features instead of reinventing the wheel!**
