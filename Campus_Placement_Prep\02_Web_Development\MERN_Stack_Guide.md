# MERN Stack Interview Guide

## Table of Contents
1. [MERN Stack Overview](#mern-stack-overview)
2. [MongoDB](#mongodb)
3. [Express.js](#expressjs)
4. [React.js](#reactjs)
5. [Node.js](#nodejs)
6. [Full Stack Integration](#full-stack-integration)
7. [Common Interview Questions](#common-interview-questions)

---

## MERN Stack Overview

### 1. What is MERN Stack?

**Answer:**
MERN is a JavaScript-based technology stack for building full-stack web applications.

**Components:**
- **M**ongoDB - NoSQL database
- **E**xpress.js - Backend web framework
- **R**eact.js - Frontend library
- **N**ode.js - JavaScript runtime environment

**Real-world analogy:** MERN stack is like a complete kitchen setup - MongoDB is the pantry (storage), Express is the stove (cooking logic), React is the presentation (plating), and Node.js is the chef (execution environment).

**Architecture Flow:**
```
Client (React) ↔ Server (Express + Node.js) ↔ Database (MongoDB)
```

---

## MongoDB

### 2. MongoDB Fundamentals

**Answer:**
MongoDB is a NoSQL document database that stores data in flexible, JSON-like documents.

```javascript
// Document structure
{
  _id: ObjectId("507f1f77bcf86cd799439011"),
  name: "John Doe",
  email: "<EMAIL>",
  age: 30,
  address: {
    street: "123 Main St",
    city: "New York",
    zipCode: "10001"
  },
  hobbies: ["reading", "swimming", "coding"],
  createdAt: ISODate("2024-01-15T10:30:00Z")
}
```

**Key Features:**
- Schema-less (flexible structure)
- Horizontal scaling
- Rich query language
- Built-in replication and sharding

### 3. MongoDB Operations

```javascript
// Connection
const mongoose = require('mongoose');
mongoose.connect('mongodb://localhost:27017/myapp');

// Schema definition
const userSchema = new mongoose.Schema({
  name: { type: String, required: true },
  email: { type: String, required: true, unique: true },
  age: { type: Number, min: 0 },
  createdAt: { type: Date, default: Date.now }
});

const User = mongoose.model('User', userSchema);

// CRUD Operations
// Create
const newUser = new User({
  name: "John Doe",
  email: "<EMAIL>",
  age: 30
});
await newUser.save();

// Read
const users = await User.find({ age: { $gte: 18 } });
const user = await User.findById(userId);
const user = await User.findOne({ email: "<EMAIL>" });

// Update
await User.findByIdAndUpdate(userId, { age: 31 });
await User.updateMany({ age: { $lt: 18 } }, { status: "minor" });

// Delete
await User.findByIdAndDelete(userId);
await User.deleteMany({ status: "inactive" });

// Aggregation
const result = await User.aggregate([
  { $match: { age: { $gte: 18 } } },
  { $group: { _id: "$city", count: { $sum: 1 } } },
  { $sort: { count: -1 } }
]);
```

---

## Express.js

### 4. Express.js Fundamentals

**Answer:**
Express.js is a minimal and flexible Node.js web application framework.

```javascript
const express = require('express');
const app = express();

// Middleware
app.use(express.json()); // Parse JSON bodies
app.use(express.urlencoded({ extended: true })); // Parse URL-encoded bodies

// CORS middleware
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  next();
});

// Routes
app.get('/', (req, res) => {
  res.json({ message: 'Welcome to the API' });
});

// Route parameters
app.get('/users/:id', (req, res) => {
  const userId = req.params.id;
  res.json({ userId });
});

// Query parameters
app.get('/search', (req, res) => {
  const { q, limit = 10 } = req.query;
  res.json({ query: q, limit: parseInt(limit) });
});

// POST route
app.post('/users', async (req, res) => {
  try {
    const { name, email } = req.body;
    const user = new User({ name, email });
    await user.save();
    res.status(201).json(user);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});

app.listen(3000, () => {
  console.log('Server running on port 3000');
});
```

### 5. Express Middleware

```javascript
// Custom middleware
const logger = (req, res, next) => {
  console.log(`${req.method} ${req.path} - ${new Date().toISOString()}`);
  next();
};

// Authentication middleware
const authenticate = (req, res, next) => {
  const token = req.header('Authorization')?.replace('Bearer ', '');
  
  if (!token) {
    return res.status(401).json({ error: 'Access denied' });
  }
  
  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    req.user = decoded;
    next();
  } catch (error) {
    res.status(400).json({ error: 'Invalid token' });
  }
};

// Error handling middleware
const errorHandler = (err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ error: 'Something went wrong!' });
};

// Using middleware
app.use(logger);
app.use('/api/protected', authenticate);
app.use(errorHandler);

// Route-specific middleware
app.get('/admin', authenticate, (req, res) => {
  res.json({ message: 'Admin area', user: req.user });
});
```

---

## React.js

### 6. React Components and JSX

```jsx
// Functional Component
import React, { useState, useEffect } from 'react';

const UserProfile = ({ userId }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchUser = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/users/${userId}`);
        if (!response.ok) throw new Error('User not found');
        const userData = await response.json();
        setUser(userData);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    if (userId) {
      fetchUser();
    }
  }, [userId]);

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error}</div>;
  if (!user) return <div>No user found</div>;

  return (
    <div className="user-profile">
      <h2>{user.name}</h2>
      <p>Email: {user.email}</p>
      <p>Age: {user.age}</p>
    </div>
  );
};

// Class Component (legacy but still important to know)
class UserList extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      users: [],
      loading: true
    };
  }

  async componentDidMount() {
    try {
      const response = await fetch('/api/users');
      const users = await response.json();
      this.setState({ users, loading: false });
    } catch (error) {
      console.error('Error fetching users:', error);
      this.setState({ loading: false });
    }
  }

  render() {
    const { users, loading } = this.state;

    if (loading) return <div>Loading...</div>;

    return (
      <ul>
        {users.map(user => (
          <li key={user._id}>
            <UserProfile userId={user._id} />
          </li>
        ))}
      </ul>
    );
  }
}
```

### 7. React Hooks

```jsx
import React, { useState, useEffect, useContext, useReducer, useMemo, useCallback } from 'react';

// useState Hook
const Counter = () => {
  const [count, setCount] = useState(0);
  
  return (
    <div>
      <p>Count: {count}</p>
      <button onClick={() => setCount(count + 1)}>Increment</button>
      <button onClick={() => setCount(prev => prev - 1)}>Decrement</button>
    </div>
  );
};

// useEffect Hook
const DataFetcher = ({ url }) => {
  const [data, setData] = useState(null);
  
  useEffect(() => {
    let cancelled = false;
    
    const fetchData = async () => {
      try {
        const response = await fetch(url);
        const result = await response.json();
        if (!cancelled) {
          setData(result);
        }
      } catch (error) {
        if (!cancelled) {
          console.error('Error:', error);
        }
      }
    };
    
    fetchData();
    
    // Cleanup function
    return () => {
      cancelled = true;
    };
  }, [url]); // Dependency array
  
  return <div>{data ? JSON.stringify(data) : 'Loading...'}</div>;
};

// useContext Hook
const ThemeContext = React.createContext();

const ThemeProvider = ({ children }) => {
  const [theme, setTheme] = useState('light');
  
  return (
    <ThemeContext.Provider value={{ theme, setTheme }}>
      {children}
    </ThemeContext.Provider>
  );
};

const ThemedComponent = () => {
  const { theme, setTheme } = useContext(ThemeContext);
  
  return (
    <div className={`theme-${theme}`}>
      <p>Current theme: {theme}</p>
      <button onClick={() => setTheme(theme === 'light' ? 'dark' : 'light')}>
        Toggle Theme
      </button>
    </div>
  );
};

// useReducer Hook
const todoReducer = (state, action) => {
  switch (action.type) {
    case 'ADD_TODO':
      return [...state, { id: Date.now(), text: action.text, completed: false }];
    case 'TOGGLE_TODO':
      return state.map(todo =>
        todo.id === action.id ? { ...todo, completed: !todo.completed } : todo
      );
    case 'DELETE_TODO':
      return state.filter(todo => todo.id !== action.id);
    default:
      return state;
  }
};

const TodoApp = () => {
  const [todos, dispatch] = useReducer(todoReducer, []);
  const [inputText, setInputText] = useState('');
  
  const addTodo = () => {
    if (inputText.trim()) {
      dispatch({ type: 'ADD_TODO', text: inputText });
      setInputText('');
    }
  };
  
  return (
    <div>
      <input
        value={inputText}
        onChange={(e) => setInputText(e.target.value)}
        placeholder="Add todo"
      />
      <button onClick={addTodo}>Add</button>
      <ul>
        {todos.map(todo => (
          <li key={todo.id}>
            <span
              style={{ textDecoration: todo.completed ? 'line-through' : 'none' }}
              onClick={() => dispatch({ type: 'TOGGLE_TODO', id: todo.id })}
            >
              {todo.text}
            </span>
            <button onClick={() => dispatch({ type: 'DELETE_TODO', id: todo.id })}>
              Delete
            </button>
          </li>
        ))}
      </ul>
    </div>
  );
};

// useMemo and useCallback for performance
const ExpensiveComponent = ({ items, filter }) => {
  // Memoize expensive calculation
  const filteredItems = useMemo(() => {
    console.log('Filtering items...');
    return items.filter(item => item.name.includes(filter));
  }, [items, filter]);
  
  // Memoize callback function
  const handleClick = useCallback((id) => {
    console.log('Item clicked:', id);
  }, []);
  
  return (
    <ul>
      {filteredItems.map(item => (
        <li key={item.id} onClick={() => handleClick(item.id)}>
          {item.name}
        </li>
      ))}
    </ul>
  );
};
```

---

## Node.js

### 8. Node.js Fundamentals

```javascript
// File system operations
const fs = require('fs').promises;
const path = require('path');

// Read file
const readFile = async (filename) => {
  try {
    const data = await fs.readFile(path.join(__dirname, filename), 'utf8');
    return data;
  } catch (error) {
    console.error('Error reading file:', error);
    throw error;
  }
};

// Write file
const writeFile = async (filename, data) => {
  try {
    await fs.writeFile(path.join(__dirname, filename), data, 'utf8');
    console.log('File written successfully');
  } catch (error) {
    console.error('Error writing file:', error);
    throw error;
  }
};

// HTTP server
const http = require('http');
const url = require('url');

const server = http.createServer((req, res) => {
  const parsedUrl = url.parse(req.url, true);
  const { pathname, query } = parsedUrl;
  
  res.setHeader('Content-Type', 'application/json');
  
  if (pathname === '/api/users' && req.method === 'GET') {
    res.statusCode = 200;
    res.end(JSON.stringify({ users: [], query }));
  } else {
    res.statusCode = 404;
    res.end(JSON.stringify({ error: 'Not found' }));
  }
});

server.listen(3000, () => {
  console.log('Server running on port 3000');
});

// Environment variables
const config = {
  port: process.env.PORT || 3000,
  dbUrl: process.env.DB_URL || 'mongodb://localhost:27017/myapp',
  jwtSecret: process.env.JWT_SECRET || 'fallback-secret'
};

// Process events
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});
```

---

## Full Stack Integration

### 9. API Integration in React

```jsx
// API service
class ApiService {
  constructor(baseURL = '/api') {
    this.baseURL = baseURL;
  }
  
  async request(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const config = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    };
    
    if (config.body && typeof config.body === 'object') {
      config.body = JSON.stringify(config.body);
    }
    
    const response = await fetch(url, config);
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    return response.json();
  }
  
  get(endpoint) {
    return this.request(endpoint);
  }
  
  post(endpoint, data) {
    return this.request(endpoint, {
      method: 'POST',
      body: data
    });
  }
  
  put(endpoint, data) {
    return this.request(endpoint, {
      method: 'PUT',
      body: data
    });
  }
  
  delete(endpoint) {
    return this.request(endpoint, {
      method: 'DELETE'
    });
  }
}

// Custom hook for API calls
const useApi = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  
  const apiService = useMemo(() => new ApiService(), []);
  
  const execute = useCallback(async (apiCall) => {
    try {
      setLoading(true);
      setError(null);
      const result = await apiCall();
      return result;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);
  
  return { execute, loading, error, apiService };
};

// Using the API hook
const UserManager = () => {
  const [users, setUsers] = useState([]);
  const { execute, loading, error, apiService } = useApi();
  
  useEffect(() => {
    const fetchUsers = async () => {
      try {
        const userData = await execute(() => apiService.get('/users'));
        setUsers(userData);
      } catch (err) {
        console.error('Failed to fetch users:', err);
      }
    };
    
    fetchUsers();
  }, [execute, apiService]);
  
  const createUser = async (userData) => {
    try {
      const newUser = await execute(() => apiService.post('/users', userData));
      setUsers(prev => [...prev, newUser]);
    } catch (err) {
      console.error('Failed to create user:', err);
    }
  };
  
  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error}</div>;
  
  return (
    <div>
      <h2>Users</h2>
      <ul>
        {users.map(user => (
          <li key={user._id}>{user.name} - {user.email}</li>
        ))}
      </ul>
    </div>
  );
};
```

---

## Common Interview Questions

### 10. What are the advantages of MERN stack?

**Answer:**
1. **Single Language** - JavaScript throughout the stack
2. **JSON Data Flow** - Consistent data format
3. **Rich Ecosystem** - Large npm package repository
4. **Fast Development** - Reusable components and code
5. **Scalability** - Each component can scale independently
6. **Community Support** - Large developer community

### 11. Explain the difference between SQL and NoSQL databases

**Answer:**

| Aspect | SQL (Relational) | NoSQL (MongoDB) |
|--------|------------------|-----------------|
| Schema | Fixed schema | Flexible schema |
| Data Structure | Tables with rows/columns | Documents (JSON-like) |
| Relationships | Foreign keys, JOINs | Embedded documents, references |
| Scaling | Vertical (scale up) | Horizontal (scale out) |
| ACID | Full ACID compliance | Eventual consistency |
| Query Language | SQL | MongoDB Query Language |

**When to use MongoDB:**
- Rapid development with changing requirements
- Handling large volumes of unstructured data
- Need for horizontal scaling
- Real-time applications

**When to use SQL:**
- Complex relationships between data
- Need for ACID transactions
- Well-defined schema requirements
- Complex queries and reporting

### Key Interview Tips:
1. **Understand the full request-response cycle**
2. **Know how to handle errors gracefully**
3. **Understand state management** (Redux, Context API)
4. **Be familiar with deployment** (Heroku, AWS, Netlify)
5. **Know testing frameworks** (Jest, React Testing Library)
6. **Understand security best practices** (JWT, CORS, validation)

### Practice Projects:
1. Build a full-stack todo application
2. Create a blog with user authentication
3. Develop an e-commerce platform
4. Build a real-time chat application
5. Create a social media dashboard

Remember: **Focus on understanding how all the pieces work together - the magic of MERN is in the seamless integration!**
