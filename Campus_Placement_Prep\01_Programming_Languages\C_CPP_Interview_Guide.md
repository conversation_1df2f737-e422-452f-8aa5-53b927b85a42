# C/C++ Interview Questions & Concepts Guide

## Table of Contents
1. [Basic Concepts](#basic-concepts)
2. [Memory Management](#memory-management)
3. [Object-Oriented Programming](#object-oriented-programming)
4. [Advanced Topics](#advanced-topics)
5. [Common Interview Questions](#common-interview-questions)
6. [Real-World Problem Solving](#real-world-problem-solving)

---

## Basic Concepts

### 1. What is the difference between C and C++?

**Answer:**
- **C** is a procedural programming language, while **C++** is an object-oriented programming language
- C++ is a superset of C with additional features like classes, objects, inheritance, polymorphism
- C uses functions for code organization, C++ uses classes and objects
- C has no concept of access specifiers, C++ has public, private, protected

**Real-world analogy:** Think of C as a toolbox with individual tools, while C++ is like a workshop with organized workstations (classes) that contain related tools and materials.

### 2. Explain the compilation process in C/C++

**Answer:**
1. **Preprocessing** - Handles directives like #include, #define
2. **Compilation** - Converts source code to assembly language
3. **Assembly** - Converts assembly to machine code (object files)
4. **Linking** - Combines object files and libraries to create executable

**Real-world analogy:** Like building a house - preprocessing is gathering materials, compilation is creating blueprints, assembly is making components, linking is assembling everything together.

### 3. What are pointers and why are they important?

**Answer:**
Pointers are variables that store memory addresses of other variables.

```cpp
int x = 10;
int* ptr = &x;  // ptr stores address of x
cout << *ptr;   // Dereferences ptr to get value 10
```

**Benefits:**
- Dynamic memory allocation
- Efficient array and string handling
- Function parameter passing by reference
- Data structure implementation (linked lists, trees)

**Real-world analogy:** A pointer is like a house address - it tells you where to find something, not what's actually there.

---

## Memory Management

### 4. Stack vs Heap Memory

**Stack:**
- Automatic memory management
- Fast allocation/deallocation
- Limited size
- LIFO (Last In, First Out)
- Local variables, function parameters

**Heap:**
- Manual memory management (new/delete)
- Slower allocation/deallocation
- Large size
- Random access
- Dynamic memory allocation

```cpp
// Stack allocation
int arr[100];  // Fixed size, automatic cleanup

// Heap allocation
int* arr = new int[100];  // Dynamic size, manual cleanup required
delete[] arr;  // Must free memory
```

**Real-world analogy:** Stack is like a cafeteria tray stack (last tray in, first out), Heap is like a parking lot (park anywhere, but remember your spot).

### 5. Memory Leaks and How to Prevent Them

**Answer:**
Memory leaks occur when dynamically allocated memory is not freed.

```cpp
// Memory leak example
void badFunction() {
    int* ptr = new int[1000];
    // Function ends without delete[] ptr
    // Memory is leaked!
}

// Correct approach
void goodFunction() {
    int* ptr = new int[1000];
    // ... use ptr
    delete[] ptr;  // Free memory
    ptr = nullptr; // Avoid dangling pointer
}
```

**Prevention strategies:**
- Always pair new with delete, new[] with delete[]
- Use smart pointers (unique_ptr, shared_ptr)
- RAII (Resource Acquisition Is Initialization)
- Use containers like vector instead of raw arrays

---

## Object-Oriented Programming

### 6. Four Pillars of OOP

**1. Encapsulation:**
Bundling data and methods together, hiding internal details.

```cpp
class BankAccount {
private:
    double balance;  // Hidden from outside
public:
    void deposit(double amount) { balance += amount; }
    double getBalance() { return balance; }
};
```

**2. Inheritance:**
Creating new classes based on existing classes.

```cpp
class Vehicle {
protected:
    string brand;
public:
    void start() { cout << "Vehicle starting"; }
};

class Car : public Vehicle {
public:
    void honk() { cout << "Car honking"; }
};
```

**3. Polymorphism:**
Same interface, different implementations.

```cpp
class Shape {
public:
    virtual double area() = 0;  // Pure virtual function
};

class Circle : public Shape {
public:
    double area() override { return 3.14 * radius * radius; }
};
```

**4. Abstraction:**
Hiding complex implementation details, showing only essential features.

**Real-world analogy:** A car - you know how to drive (interface) without knowing engine mechanics (implementation).

### 7. Virtual Functions and Polymorphism

**Answer:**
Virtual functions enable runtime polymorphism (late binding).

```cpp
class Animal {
public:
    virtual void makeSound() { cout << "Some sound"; }
};

class Dog : public Animal {
public:
    void makeSound() override { cout << "Woof!"; }
};

class Cat : public Animal {
public:
    void makeSound() override { cout << "Meow!"; }
};

// Runtime polymorphism
Animal* animals[] = {new Dog(), new Cat()};
for(Animal* animal : animals) {
    animal->makeSound();  // Calls appropriate derived class method
}
```

**Key points:**
- Virtual function table (vtable) stores function pointers
- Enables dynamic dispatch
- Pure virtual functions create abstract classes

---

## Advanced Topics

### 8. Function Overloading vs Function Overriding

**Function Overloading (Compile-time polymorphism):**
```cpp
class Calculator {
public:
    int add(int a, int b) { return a + b; }
    double add(double a, double b) { return a + b; }
    int add(int a, int b, int c) { return a + b + c; }
};
```

**Function Overriding (Runtime polymorphism):**
```cpp
class Base {
public:
    virtual void display() { cout << "Base display"; }
};

class Derived : public Base {
public:
    void display() override { cout << "Derived display"; }
};
```

### 9. Smart Pointers (C++11 and later)

**Answer:**
Smart pointers automatically manage memory, preventing leaks.

```cpp
#include <memory>

// unique_ptr - exclusive ownership
std::unique_ptr<int> ptr1 = std::make_unique<int>(42);

// shared_ptr - shared ownership
std::shared_ptr<int> ptr2 = std::make_shared<int>(42);
std::shared_ptr<int> ptr3 = ptr2;  // Reference count = 2

// weak_ptr - non-owning observer
std::weak_ptr<int> weak = ptr2;
```

**Benefits:**
- Automatic memory management
- Exception safety
- Clear ownership semantics

---

## Common Interview Questions

### 10. What happens when you don't provide a destructor?

**Answer:**
- Compiler provides a default destructor
- Default destructor only calls destructors of member objects
- For classes with dynamic memory, you need custom destructor

```cpp
class MyClass {
private:
    int* data;
public:
    MyClass() { data = new int[100]; }
    
    // Without custom destructor, memory leak occurs
    ~MyClass() { delete[] data; }  // Custom destructor needed
};
```

### 11. Explain the Rule of Three/Five

**Rule of Three:** If you need custom destructor, copy constructor, or copy assignment operator, you probably need all three.

**Rule of Five:** Adds move constructor and move assignment operator (C++11).

```cpp
class MyClass {
private:
    int* data;
    size_t size;

public:
    // Constructor
    MyClass(size_t s) : size(s), data(new int[s]) {}
    
    // Destructor
    ~MyClass() { delete[] data; }
    
    // Copy constructor
    MyClass(const MyClass& other) : size(other.size), data(new int[size]) {
        std::copy(other.data, other.data + size, data);
    }
    
    // Copy assignment operator
    MyClass& operator=(const MyClass& other) {
        if (this != &other) {
            delete[] data;
            size = other.size;
            data = new int[size];
            std::copy(other.data, other.data + size, data);
        }
        return *this;
    }
    
    // Move constructor (C++11)
    MyClass(MyClass&& other) noexcept : size(other.size), data(other.data) {
        other.data = nullptr;
        other.size = 0;
    }
    
    // Move assignment operator (C++11)
    MyClass& operator=(MyClass&& other) noexcept {
        if (this != &other) {
            delete[] data;
            data = other.data;
            size = other.size;
            other.data = nullptr;
            other.size = 0;
        }
        return *this;
    }
};
```

---

## Real-World Problem Solving

### 12. Design a Simple String Class

**Problem:** Implement a basic string class with essential operations.

```cpp
class MyString {
private:
    char* str;
    size_t length;

public:
    // Constructor
    MyString(const char* s = "") {
        length = strlen(s);
        str = new char[length + 1];
        strcpy(str, s);
    }
    
    // Copy constructor
    MyString(const MyString& other) : length(other.length) {
        str = new char[length + 1];
        strcpy(str, other.str);
    }
    
    // Assignment operator
    MyString& operator=(const MyString& other) {
        if (this != &other) {
            delete[] str;
            length = other.length;
            str = new char[length + 1];
            strcpy(str, other.str);
        }
        return *this;
    }
    
    // Destructor
    ~MyString() { delete[] str; }
    
    // Utility functions
    size_t size() const { return length; }
    const char* c_str() const { return str; }
    
    // Concatenation operator
    MyString operator+(const MyString& other) const {
        char* temp = new char[length + other.length + 1];
        strcpy(temp, str);
        strcat(temp, other.str);
        MyString result(temp);
        delete[] temp;
        return result;
    }
};
```

### Key Interview Tips:
1. **Always explain your thought process**
2. **Consider edge cases** (null pointers, empty inputs)
3. **Discuss time and space complexity**
4. **Mention real-world applications**
5. **Be ready to optimize your solution**

### Practice Problems:
1. Implement a linked list with all basic operations
2. Create a simple memory pool allocator
3. Design a thread-safe singleton class
4. Implement operator overloading for a complex number class
5. Write a function to detect memory leaks

Remember: **Understanding concepts is more important than memorizing syntax!**
