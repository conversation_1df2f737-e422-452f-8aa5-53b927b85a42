# 🎵 Emotion-Based Music Recommender

An intelligent web application that detects human emotions in real-time using computer vision and recommends personalized music based on the detected emotional state.

## 🌟 Features

- **Real-time Emotion Detection**: Uses advanced computer vision to analyze facial expressions and hand gestures
- **Live Video Processing**: Webcam integration for continuous emotion monitoring
- **Personalized Music Recommendations**: Suggests music based on detected emotions, preferred language, and artist
- **Interactive Web Interface**: User-friendly Streamlit-based GUI
- **Multi-modal Analysis**: Combines facial landmarks and hand gesture recognition for accurate emotion detection

## 🚀 Demo

### Video Explanation

[![Emotion based music | ai | deep learning project | with code | ml project](https://img.youtube.com/vi/uDzLxos0lNU/0.jpg)](https://youtu.be/uDzLxos0lNU)

**Watch the complete tutorial**: [Emotion based music | ai | deep learning project | with code | ml project](https://youtu.be/uDzLxos0lNU)

## 🛠️ Technology Stack

### Core Technologies

- **Python 3.8+**: Primary programming language
- **Streamlit**: Web application framework for rapid prototyping
- **OpenCV**: Computer vision library for image processing
- **MediaPipe**: Google's framework for building multimodal applied ML pipelines
- **TensorFlow/Keras**: Deep learning framework for emotion classification
- **NumPy**: Numerical computing library

### Key Libraries

- `streamlit-webrtc`: Real-time video streaming in web browsers
- `av`: Audio/video processing
- `mediapipe`: Face mesh and hand landmark detection
- `opencv-python`: Image processing and computer vision
- `tensorflow`: Machine learning model inference

## 📋 Prerequisites

- Python 3.8 or higher
- Webcam/Camera access
- Modern web browser (Chrome, Firefox, Safari)
- Stable internet connection (for YouTube recommendations)

## 🔧 Installation

1. **Clone the repository**

   ```bash
   git clone https://github.com/yourusername/emotion-music-recommender.git
   cd emotion-music-recommender
   ```

2. **Create virtual environment** (recommended)

   ```bash
   python -m venv emotion_env
   source emotion_env/bin/activate  # On Windows: emotion_env\Scripts\activate
   ```

3. **Install dependencies**

   ```bash
   pip install streamlit streamlit-webrtc opencv-python mediapipe tensorflow numpy av
   ```

4. **Verify model files**
   Ensure these files are present:
   - `model.h5` - Pre-trained emotion classification model
   - `labels.npy` - Emotion class labels

## 🎯 Usage

1. **Start the application**

   ```bash
   streamlit run music.py
   ```

2. **Open your browser**
   Navigate to `http://localhost:8501`

3. **Configure preferences**

   - Enter your preferred language (e.g., "English", "Hindi", "Spanish")
   - Enter your favorite singer/artist

4. **Emotion Detection**

   - Allow camera access when prompted
   - Position yourself in front of the camera
   - The app will detect and display your emotion in real-time

5. **Get Recommendations**
   - Click "Recommend me songs" button
   - The app will open YouTube with personalized music suggestions

## 🧠 How It Works

### 1. **Face and Hand Detection**

- Uses MediaPipe Holistic model to detect 468 facial landmarks
- Captures left and right hand landmarks (21 points each)
- Normalizes coordinates relative to reference points

### 2. **Feature Extraction**

- Extracts relative positions of facial landmarks
- Processes hand gesture coordinates
- Creates a feature vector of 1404 dimensions (468×2 + 21×2 + 21×2)

### 3. **Emotion Classification**

- Pre-trained neural network processes the feature vector
- Classifies emotions into categories (Happy, Sad, Angry, Surprised, etc.)
- Real-time prediction with confidence scoring

### 4. **Music Recommendation**

- Combines detected emotion with user preferences
- Generates YouTube search query: `{language} + {emotion} + song + {artist}`
- Opens personalized music recommendations

## 📊 Model Architecture

The emotion classification model uses:

- **Input Layer**: 1404 features (facial + hand landmarks)
- **Hidden Layers**: Dense layers with ReLU activation
- **Output Layer**: Softmax activation for emotion classification
- **Training**: Supervised learning on emotion-labeled facial expression dataset

### Related Projects

- **Data Collection**: [Watch Tutorial](https://youtu.be/ZxZSGRdTLtE)
- **Model Training**: [Watch Tutorial](https://youtu.be/He_oZ-MnIrU)
- **Live Emoji Project**: [GitHub Repository](https://github.com/Pawandeep-prog/liveEmoji)

## 🎨 Supported Emotions

- 😊 Happy
- 😢 Sad
- 😠 Angry
- 😮 Surprised
- 😨 Fear
- 🤢 Disgust
- 😐 Neutral

## 🔒 Privacy & Security

- **Local Processing**: All emotion detection happens locally on your device
- **No Data Storage**: Facial data is processed in real-time and not stored
- **Camera Access**: Only used during active sessions, can be revoked anytime
- **No Personal Information**: Only emotion labels are temporarily saved

## 🚨 Troubleshooting

### Common Issues

1. **Model Loading Error**

   ```
   AttributeError: 'NoneType' object has no attribute 'pop'
   ```

   **Solution**: The app includes automatic fallback loading with `compile=False`

2. **Camera Access Denied**

   - Check browser permissions for camera access
   - Ensure no other applications are using the camera

3. **Slow Performance**
   - Close unnecessary browser tabs
   - Ensure good lighting for better face detection
   - Check system resources (CPU/Memory)


## 📈 Future Enhancements

- [ ] Support for multiple users simultaneously
- [ ] Integration with Spotify/Apple Music APIs
- [ ] Emotion history tracking and analytics
- [ ] Voice-based emotion detection
- [ ] Mobile app development
- [ ] Advanced emotion categories (stress, excitement, etc.)
- [ ] Playlist generation based on emotion patterns



## 🙏 Acknowledgments

- Google MediaPipe team for the excellent computer vision framework
- Streamlit team for the amazing web app framework
- TensorFlow team for the machine learning capabilities
- OpenCV community for computer vision tools



