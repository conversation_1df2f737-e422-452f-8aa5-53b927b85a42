# Quick Reference Cards for Campus Placements

## 🚀 Programming Languages Quick Reference

### Python Essentials
```python
# Data Types
list = [1, 2, 3]              # Mutable, ordered
tuple = (1, 2, 3)             # Immutable, ordered
dict = {'a': 1, 'b': 2}       # Key-value pairs
set = {1, 2, 3}               # Unique elements

# List Comprehensions
squares = [x**2 for x in range(10) if x % 2 == 0]

# Lambda Functions
add = lambda x, y: x + y

# Common Methods
list.append(item)             # Add to end
list.extend(iterable)         # Add multiple items
list.pop(index)               # Remove and return
''.join(list)                 # Join list to string
```

### C++ Essentials
```cpp
// STL Containers
vector<int> v = {1, 2, 3};    // Dynamic array
map<string, int> m;           // Key-value pairs
set<int> s;                   // Unique elements
queue<int> q;                 // FIFO
stack<int> st;                // LIFO

// Algorithms
sort(v.begin(), v.end());     // Sort vector
find(v.begin(), v.end(), x);  // Find element
binary_search(v.begin(), v.end(), x);  // Binary search

// Smart Pointers
unique_ptr<int> ptr = make_unique<int>(42);
shared_ptr<int> sptr = make_shared<int>(42);
```

### SQL Quick Commands
```sql
-- Basic Queries
SELECT column FROM table WHERE condition;
INSERT INTO table (col1, col2) VALUES (val1, val2);
UPDATE table SET col1 = val1 WHERE condition;
DELETE FROM table WHERE condition;

-- Joins
INNER JOIN table2 ON table1.id = table2.id;
LEFT JOIN table2 ON table1.id = table2.id;

-- Aggregations
SELECT COUNT(*), SUM(col), AVG(col), MAX(col), MIN(col)
FROM table GROUP BY column HAVING condition;

-- Window Functions
ROW_NUMBER() OVER (PARTITION BY col ORDER BY col2);
RANK() OVER (ORDER BY col);
```

---

## 🧮 Data Structures & Algorithms

### Time Complexities
| Operation | Array | Linked List | Stack | Queue | BST | Hash Table |
|-----------|-------|-------------|-------|-------|-----|------------|
| Access    | O(1)  | O(n)        | O(n)  | O(n)  | O(log n) | O(1) |
| Search    | O(n)  | O(n)        | O(n)  | O(n)  | O(log n) | O(1) |
| Insert    | O(n)  | O(1)        | O(1)  | O(1)  | O(log n) | O(1) |
| Delete    | O(n)  | O(1)        | O(1)  | O(1)  | O(log n) | O(1) |

### Sorting Algorithms
| Algorithm | Best | Average | Worst | Space | Stable |
|-----------|------|---------|-------|-------|--------|
| Bubble    | O(n) | O(n²)   | O(n²) | O(1)  | Yes    |
| Selection | O(n²)| O(n²)   | O(n²) | O(1)  | No     |
| Insertion | O(n) | O(n²)   | O(n²) | O(1)  | Yes    |
| Merge     | O(n log n) | O(n log n) | O(n log n) | O(n) | Yes |
| Quick     | O(n log n) | O(n log n) | O(n²) | O(log n) | No |
| Heap      | O(n log n) | O(n log n) | O(n log n) | O(1) | No |

### Common Problem Patterns
```python
# Two Pointers
def two_sum_sorted(arr, target):
    left, right = 0, len(arr) - 1
    while left < right:
        current_sum = arr[left] + arr[right]
        if current_sum == target:
            return [left, right]
        elif current_sum < target:
            left += 1
        else:
            right -= 1

# Sliding Window
def max_sum_subarray(arr, k):
    window_sum = sum(arr[:k])
    max_sum = window_sum
    for i in range(k, len(arr)):
        window_sum = window_sum - arr[i-k] + arr[i]
        max_sum = max(max_sum, window_sum)
    return max_sum

# Binary Search
def binary_search(arr, target):
    left, right = 0, len(arr) - 1
    while left <= right:
        mid = (left + right) // 2
        if arr[mid] == target:
            return mid
        elif arr[mid] < target:
            left = mid + 1
        else:
            right = mid - 1
    return -1
```

---

## 🌐 Web Development Quick Reference

### HTML5 Semantic Elements
```html
<header>    <!-- Page/section header -->
<nav>       <!-- Navigation links -->
<main>      <!-- Main content -->
<section>   <!-- Thematic grouping -->
<article>   <!-- Independent content -->
<aside>     <!-- Sidebar content -->
<footer>    <!-- Page/section footer -->
<figure>    <!-- Media with caption -->
<time>      <!-- Date/time -->
```

### CSS Flexbox
```css
.container {
    display: flex;
    flex-direction: row | column;
    justify-content: flex-start | center | space-between | space-around;
    align-items: flex-start | center | flex-end | stretch;
    flex-wrap: nowrap | wrap;
}

.item {
    flex: 1;                    /* flex-grow: 1, flex-shrink: 1, flex-basis: 0% */
    align-self: flex-end;       /* Override container's align-items */
}
```

### JavaScript ES6+ Features
```javascript
// Arrow Functions
const add = (a, b) => a + b;

// Destructuring
const {name, age} = person;
const [first, second] = array;

// Template Literals
const message = `Hello ${name}, you are ${age} years old`;

// Promises & Async/Await
async function fetchData() {
    try {
        const response = await fetch('/api/data');
        const data = await response.json();
        return data;
    } catch (error) {
        console.error('Error:', error);
    }
}

// Spread Operator
const newArray = [...oldArray, newItem];
const newObject = {...oldObject, newProperty: value};
```

### React Hooks
```jsx
// useState
const [count, setCount] = useState(0);

// useEffect
useEffect(() => {
    // Effect logic
    return () => {
        // Cleanup
    };
}, [dependencies]);

// useContext
const value = useContext(MyContext);

// Custom Hook
function useCounter(initialValue = 0) {
    const [count, setCount] = useState(initialValue);
    const increment = () => setCount(count + 1);
    const decrement = () => setCount(count - 1);
    return { count, increment, decrement };
}
```

---

## 🤖 Machine Learning Quick Reference

### Supervised Learning Algorithms
| Algorithm | Use Case | Pros | Cons |
|-----------|----------|------|------|
| Linear Regression | Continuous prediction | Simple, interpretable | Assumes linear relationship |
| Logistic Regression | Binary classification | Probabilistic output | Linear decision boundary |
| Decision Tree | Classification/Regression | Interpretable | Prone to overfitting |
| Random Forest | Classification/Regression | Reduces overfitting | Less interpretable |
| SVM | Classification | Works with high dimensions | Slow on large datasets |
| Neural Networks | Complex patterns | Universal approximator | Requires large data |

### Model Evaluation Metrics
```python
# Classification Metrics
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score

accuracy = accuracy_score(y_true, y_pred)
precision = precision_score(y_true, y_pred)
recall = recall_score(y_true, y_pred)
f1 = f1_score(y_true, y_pred)

# Regression Metrics
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score

mse = mean_squared_error(y_true, y_pred)
mae = mean_absolute_error(y_true, y_pred)
r2 = r2_score(y_true, y_pred)
```

### Data Preprocessing
```python
# Handling Missing Data
df.dropna()                           # Remove missing values
df.fillna(df.mean())                  # Fill with mean
df.fillna(method='forward')           # Forward fill

# Encoding Categorical Variables
pd.get_dummies(df, columns=['category'])  # One-hot encoding
LabelEncoder().fit_transform(df['category'])  # Label encoding

# Feature Scaling
from sklearn.preprocessing import StandardScaler, MinMaxScaler
StandardScaler().fit_transform(X)     # Z-score normalization
MinMaxScaler().fit_transform(X)       # Min-max scaling
```

---

## 💾 Database & System Design

### Database Normalization
- **1NF**: Atomic values, no repeating groups
- **2NF**: 1NF + no partial dependencies
- **3NF**: 2NF + no transitive dependencies
- **BCNF**: 3NF + every determinant is candidate key

### ACID Properties
- **Atomicity**: All or nothing
- **Consistency**: Valid state transitions
- **Isolation**: Concurrent transactions don't interfere
- **Durability**: Committed changes persist

### System Design Concepts
```
Load Balancer → Web Servers → Application Servers → Database
                    ↓
                Cache Layer
                    ↓
                Message Queue
```

**Scalability Patterns:**
- **Horizontal Scaling**: Add more servers
- **Vertical Scaling**: Upgrade server hardware
- **Caching**: Store frequently accessed data
- **Load Balancing**: Distribute requests across servers
- **Database Sharding**: Split database across multiple servers

---

## 🔧 Operating Systems Quick Reference

### Process States
```
New → Ready → Running → Terminated
        ↑        ↓
        ← Waiting/Blocked
```

### CPU Scheduling Algorithms
- **FCFS**: First Come First Serve
- **SJF**: Shortest Job First
- **Round Robin**: Time quantum-based
- **Priority**: Based on process priority
- **Multilevel Queue**: Multiple queues with different priorities

### Memory Management
- **Paging**: Fixed-size blocks
- **Segmentation**: Variable-size blocks
- **Virtual Memory**: Illusion of large memory
- **Page Replacement**: FIFO, LRU, Optimal

### Synchronization
```python
# Producer-Consumer Problem
import threading

buffer = []
buffer_size = 10
mutex = threading.Lock()
not_full = threading.Condition(mutex)
not_empty = threading.Condition(mutex)

def producer():
    with not_full:
        while len(buffer) == buffer_size:
            not_full.wait()
        buffer.append(item)
        not_empty.notify()

def consumer():
    with not_empty:
        while len(buffer) == 0:
            not_empty.wait()
        item = buffer.pop(0)
        not_full.notify()
```

---

## 📊 Interview Preparation Checklist

### Technical Skills
- [ ] **Programming Language**: Master syntax and libraries
- [ ] **Data Structures**: Implement from scratch
- [ ] **Algorithms**: Understand time/space complexity
- [ ] **System Design**: Know basic patterns and trade-offs
- [ ] **Database**: Write complex queries and design schemas
- [ ] **Web Development**: Build full-stack applications
- [ ] **Problem Solving**: Solve 200+ coding problems

### Soft Skills
- [ ] **Communication**: Explain technical concepts clearly
- [ ] **Problem Solving**: Think aloud during coding
- [ ] **Behavioral**: Prepare STAR method stories
- [ ] **Questions**: Prepare thoughtful questions for interviewer
- [ ] **Confidence**: Practice mock interviews regularly

### Common Behavioral Questions
1. "Tell me about yourself"
2. "Why do you want to work here?"
3. "Describe a challenging project"
4. "How do you handle conflicts?"
5. "Where do you see yourself in 5 years?"
6. "Describe a time you failed"
7. "How do you learn new technologies?"

### Technical Interview Tips
1. **Clarify the problem** before coding
2. **Think aloud** while solving
3. **Start with brute force** then optimize
4. **Test your solution** with examples
5. **Discuss trade-offs** and alternatives
6. **Ask questions** about requirements
7. **Handle edge cases** appropriately

---

## 🎯 Last-Minute Review

### Day Before Interview
- [ ] Review company information and recent news
- [ ] Practice explaining your projects
- [ ] Solve 2-3 easy problems for confidence
- [ ] Prepare questions for the interviewer
- [ ] Get good sleep and stay hydrated

### During Interview
- [ ] Listen carefully to the problem
- [ ] Ask clarifying questions
- [ ] Explain your approach before coding
- [ ] Write clean, readable code
- [ ] Test your solution
- [ ] Be honest about what you don't know
- [ ] Stay calm and think step by step

### After Interview
- [ ] Send thank you email within 24 hours
- [ ] Reflect on what went well and what to improve
- [ ] Follow up appropriately if no response
- [ ] Continue practicing for future opportunities

---

**Remember**: This is your quick reference guide. Keep it handy during your final preparation and review it regularly. Confidence comes from preparation!

**Best of luck! 🚀**
