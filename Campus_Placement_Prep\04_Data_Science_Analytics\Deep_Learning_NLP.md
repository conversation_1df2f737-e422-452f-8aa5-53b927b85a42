# Deep Learning & NLP Interview Guide

## Table of Contents
1. [Deep Learning Fundamentals](#deep-learning-fundamentals)
2. [Neural Networks](#neural-networks)
3. [Convolutional Neural Networks](#convolutional-neural-networks)
4. [Recurrent Neural Networks](#recurrent-neural-networks)
5. [Natural Language Processing](#natural-language-processing)
6. [Advanced NLP](#advanced-nlp)
7. [Common Interview Questions](#common-interview-questions)

---

## Deep Learning Fundamentals

### 1. What is Deep Learning?

**Definition:** Subset of machine learning using neural networks with multiple hidden layers to model complex patterns in data.

**Key Characteristics:**
- **Multiple layers** - Deep architecture (3+ hidden layers)
- **Automatic feature learning** - No manual feature engineering
- **Non-linear transformations** - Can model complex relationships
- **Large datasets** - Requires substantial data to train effectively

**Real-world analogy:** Deep learning is like the human brain's neural network - simple neurons connected in complex ways to recognize patterns, from basic edges to complex objects.

```python
# Simple neural network structure
import tensorflow as tf
from tensorflow.keras import layers, models

# Basic deep neural network
model = models.Sequential([
    layers.Dense(128, activation='relu', input_shape=(784,)),  # Input layer
    layers.Dense(64, activation='relu'),                       # Hidden layer 1
    layers.Dense(32, activation='relu'),                       # Hidden layer 2
    layers.Dense(10, activation='softmax')                     # Output layer
])

model.compile(optimizer='adam',
              loss='categorical_crossentropy',
              metrics=['accuracy'])

print(model.summary())
```

### 2. Activation Functions

**Purpose:** Introduce non-linearity to enable learning complex patterns.

```python
import numpy as np
import matplotlib.pyplot as plt

def sigmoid(x):
    return 1 / (1 + np.exp(-x))

def tanh(x):
    return np.tanh(x)

def relu(x):
    return np.maximum(0, x)

def leaky_relu(x, alpha=0.01):
    return np.where(x > 0, x, alpha * x)

# Example usage in Keras
model = models.Sequential([
    layers.Dense(64, activation='relu'),      # ReLU activation
    layers.Dense(32, activation='tanh'),      # Tanh activation
    layers.Dense(1, activation='sigmoid')     # Sigmoid for binary classification
])
```

**Activation Function Comparison:**
- **Sigmoid** - Output range (0,1), vanishing gradient problem
- **Tanh** - Output range (-1,1), zero-centered, still vanishing gradient
- **ReLU** - Most popular, solves vanishing gradient, can cause dying neurons
- **Leaky ReLU** - Solves dying ReLU problem
- **Softmax** - Used in output layer for multi-class classification

---

## Neural Networks

### 3. Feedforward Neural Networks

**Architecture:** Information flows in one direction from input to output.

```python
# Complete example: MNIST digit classification
import tensorflow as tf
from tensorflow.keras.datasets import mnist
from tensorflow.keras.utils import to_categorical

# Load and preprocess data
(X_train, y_train), (X_test, y_test) = mnist.load_data()

# Normalize pixel values
X_train = X_train.reshape(60000, 784).astype('float32') / 255
X_test = X_test.reshape(10000, 784).astype('float32') / 255

# One-hot encode labels
y_train = to_categorical(y_train, 10)
y_test = to_categorical(y_test, 10)

# Build model
model = models.Sequential([
    layers.Dense(512, activation='relu', input_shape=(784,)),
    layers.Dropout(0.2),  # Regularization
    layers.Dense(256, activation='relu'),
    layers.Dropout(0.2),
    layers.Dense(10, activation='softmax')
])

# Compile and train
model.compile(optimizer='adam',
              loss='categorical_crossentropy',
              metrics=['accuracy'])

history = model.fit(X_train, y_train,
                    batch_size=128,
                    epochs=10,
                    validation_split=0.1,
                    verbose=1)

# Evaluate
test_loss, test_accuracy = model.evaluate(X_test, y_test, verbose=0)
print(f"Test accuracy: {test_accuracy:.4f}")
```

### 4. Backpropagation Algorithm

**Concept:** Method to train neural networks by propagating errors backward through the network.

**Steps:**
1. **Forward pass** - Calculate predictions
2. **Calculate loss** - Compare with actual values
3. **Backward pass** - Calculate gradients
4. **Update weights** - Using gradient descent

```python
# Simplified backpropagation implementation (conceptual)
class SimpleNeuralNetwork:
    def __init__(self, input_size, hidden_size, output_size):
        # Initialize weights randomly
        self.W1 = np.random.randn(input_size, hidden_size) * 0.01
        self.b1 = np.zeros((1, hidden_size))
        self.W2 = np.random.randn(hidden_size, output_size) * 0.01
        self.b2 = np.zeros((1, output_size))
    
    def forward(self, X):
        # Forward propagation
        self.z1 = np.dot(X, self.W1) + self.b1
        self.a1 = np.tanh(self.z1)  # Hidden layer activation
        self.z2 = np.dot(self.a1, self.W2) + self.b2
        self.a2 = sigmoid(self.z2)  # Output layer activation
        return self.a2
    
    def backward(self, X, y, output):
        m = X.shape[0]  # Number of examples
        
        # Backward propagation
        dz2 = output - y
        dW2 = (1/m) * np.dot(self.a1.T, dz2)
        db2 = (1/m) * np.sum(dz2, axis=0, keepdims=True)
        
        da1 = np.dot(dz2, self.W2.T)
        dz1 = da1 * (1 - np.power(self.a1, 2))  # tanh derivative
        dW1 = (1/m) * np.dot(X.T, dz1)
        db1 = (1/m) * np.sum(dz1, axis=0, keepdims=True)
        
        return dW1, db1, dW2, db2
    
    def update_parameters(self, dW1, db1, dW2, db2, learning_rate):
        self.W1 -= learning_rate * dW1
        self.b1 -= learning_rate * db1
        self.W2 -= learning_rate * dW2
        self.b2 -= learning_rate * db2
```

---

## Convolutional Neural Networks

### 5. CNN Architecture

**Purpose:** Designed for processing grid-like data (images, time series).

**Key Components:**
- **Convolutional layers** - Feature detection
- **Pooling layers** - Dimensionality reduction
- **Fully connected layers** - Classification

```python
# CNN for image classification
model = models.Sequential([
    # Convolutional layers
    layers.Conv2D(32, (3, 3), activation='relu', input_shape=(28, 28, 1)),
    layers.MaxPooling2D((2, 2)),
    
    layers.Conv2D(64, (3, 3), activation='relu'),
    layers.MaxPooling2D((2, 2)),
    
    layers.Conv2D(64, (3, 3), activation='relu'),
    
    # Flatten and dense layers
    layers.Flatten(),
    layers.Dense(64, activation='relu'),
    layers.Dropout(0.5),
    layers.Dense(10, activation='softmax')
])

model.compile(optimizer='adam',
              loss='categorical_crossentropy',
              metrics=['accuracy'])

print(model.summary())
```

### 6. CNN Operations

**Convolution Operation:**
```python
# Understanding convolution
import numpy as np

def convolution_2d(image, kernel):
    """Simple 2D convolution implementation"""
    kernel_height, kernel_width = kernel.shape
    image_height, image_width = image.shape
    
    # Output dimensions
    output_height = image_height - kernel_height + 1
    output_width = image_width - kernel_width + 1
    
    output = np.zeros((output_height, output_width))
    
    for i in range(output_height):
        for j in range(output_width):
            # Element-wise multiplication and sum
            output[i, j] = np.sum(
                image[i:i+kernel_height, j:j+kernel_width] * kernel
            )
    
    return output

# Example: Edge detection kernel
edge_kernel = np.array([[-1, -1, -1],
                       [-1,  8, -1],
                       [-1, -1, -1]])

# Sample image (5x5)
sample_image = np.random.randint(0, 256, (5, 5))
result = convolution_2d(sample_image, edge_kernel)
print("Convolution result shape:", result.shape)
```

**Pooling Operations:**
```python
def max_pooling_2d(image, pool_size=(2, 2), stride=2):
    """Max pooling implementation"""
    pool_height, pool_width = pool_size
    image_height, image_width = image.shape
    
    output_height = (image_height - pool_height) // stride + 1
    output_width = (image_width - pool_width) // stride + 1
    
    output = np.zeros((output_height, output_width))
    
    for i in range(output_height):
        for j in range(output_width):
            start_i = i * stride
            start_j = j * stride
            pool_region = image[start_i:start_i+pool_height, 
                              start_j:start_j+pool_width]
            output[i, j] = np.max(pool_region)
    
    return output

# Example usage
sample_feature_map = np.random.rand(4, 4)
pooled = max_pooling_2d(sample_feature_map)
print("Original shape:", sample_feature_map.shape)
print("After pooling:", pooled.shape)
```

---

## Recurrent Neural Networks

### 7. RNN Architecture

**Purpose:** Process sequential data by maintaining hidden state across time steps.

```python
# Simple RNN for sequence classification
from tensorflow.keras.layers import SimpleRNN, LSTM, GRU

# Basic RNN
rnn_model = models.Sequential([
    layers.SimpleRNN(50, return_sequences=True, input_shape=(timesteps, features)),
    layers.SimpleRNN(50),
    layers.Dense(1, activation='sigmoid')
])

# LSTM (Long Short-Term Memory)
lstm_model = models.Sequential([
    layers.LSTM(50, return_sequences=True, input_shape=(timesteps, features)),
    layers.LSTM(50),
    layers.Dense(1, activation='sigmoid')
])

# GRU (Gated Recurrent Unit)
gru_model = models.Sequential([
    layers.GRU(50, return_sequences=True, input_shape=(timesteps, features)),
    layers.GRU(50),
    layers.Dense(1, activation='sigmoid')
])
```

### 8. LSTM and GRU

**LSTM Components:**
- **Forget gate** - Decides what to forget from cell state
- **Input gate** - Decides what new information to store
- **Output gate** - Controls what parts of cell state to output

```python
# Text generation with LSTM
import tensorflow as tf
from tensorflow.keras.preprocessing.text import Tokenizer
from tensorflow.keras.preprocessing.sequence import pad_sequences

# Sample text preprocessing
text = "Your sample text here for training..."
tokenizer = Tokenizer()
tokenizer.fit_on_texts([text])
total_words = len(tokenizer.word_index) + 1

# Create input sequences
input_sequences = []
for line in text.split('\n'):
    token_list = tokenizer.texts_to_sequences([line])[0]
    for i in range(1, len(token_list)):
        n_gram_sequence = token_list[:i+1]
        input_sequences.append(n_gram_sequence)

# Pad sequences
max_sequence_len = max([len(seq) for seq in input_sequences])
input_sequences = pad_sequences(input_sequences, maxlen=max_sequence_len, padding='pre')

# Create predictors and labels
X = input_sequences[:, :-1]
y = input_sequences[:, -1]
y = tf.keras.utils.to_categorical(y, num_classes=total_words)

# Build LSTM model
model = models.Sequential([
    layers.Embedding(total_words, 100, input_length=max_sequence_len-1),
    layers.LSTM(150, return_sequences=True),
    layers.Dropout(0.2),
    layers.LSTM(100),
    layers.Dense(total_words, activation='softmax')
])

model.compile(loss='categorical_crossentropy', optimizer='adam', metrics=['accuracy'])
```

---

## Natural Language Processing

### 9. Text Preprocessing

**Basic NLP Pipeline:**
```python
import nltk
import re
from nltk.corpus import stopwords
from nltk.tokenize import word_tokenize
from nltk.stem import PorterStemmer, WordNetLemmatizer
from sklearn.feature_extraction.text import TfidfVectorizer

# Download required NLTK data
nltk.download('punkt')
nltk.download('stopwords')
nltk.download('wordnet')

class TextPreprocessor:
    def __init__(self):
        self.stop_words = set(stopwords.words('english'))
        self.stemmer = PorterStemmer()
        self.lemmatizer = WordNetLemmatizer()
    
    def clean_text(self, text):
        """Basic text cleaning"""
        # Convert to lowercase
        text = text.lower()
        
        # Remove special characters and digits
        text = re.sub(r'[^a-zA-Z\s]', '', text)
        
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text).strip()
        
        return text
    
    def tokenize(self, text):
        """Tokenize text into words"""
        return word_tokenize(text)
    
    def remove_stopwords(self, tokens):
        """Remove common stopwords"""
        return [token for token in tokens if token not in self.stop_words]
    
    def stem_tokens(self, tokens):
        """Apply stemming to tokens"""
        return [self.stemmer.stem(token) for token in tokens]
    
    def lemmatize_tokens(self, tokens):
        """Apply lemmatization to tokens"""
        return [self.lemmatizer.lemmatize(token) for token in tokens]
    
    def preprocess(self, text, use_stemming=True):
        """Complete preprocessing pipeline"""
        # Clean text
        text = self.clean_text(text)
        
        # Tokenize
        tokens = self.tokenize(text)
        
        # Remove stopwords
        tokens = self.remove_stopwords(tokens)
        
        # Stem or lemmatize
        if use_stemming:
            tokens = self.stem_tokens(tokens)
        else:
            tokens = self.lemmatize_tokens(tokens)
        
        return ' '.join(tokens)

# Example usage
preprocessor = TextPreprocessor()
sample_text = "The quick brown foxes are running through the beautiful forest!"
processed = preprocessor.preprocess(sample_text)
print(f"Original: {sample_text}")
print(f"Processed: {processed}")
```

### 10. Feature Extraction

**TF-IDF (Term Frequency-Inverse Document Frequency):**
```python
from sklearn.feature_extraction.text import TfidfVectorizer
import numpy as np

# Sample documents
documents = [
    "The cat sat on the mat",
    "The dog ran in the park",
    "Cats and dogs are pets",
    "I love my pet cat"
]

# TF-IDF Vectorization
tfidf = TfidfVectorizer(max_features=100, stop_words='english')
tfidf_matrix = tfidf.fit_transform(documents)

# Get feature names
feature_names = tfidf.get_feature_names_out()

print("TF-IDF Matrix shape:", tfidf_matrix.shape)
print("Feature names:", feature_names[:10])

# Convert to dense array for viewing
tfidf_dense = tfidf_matrix.todense()
print("TF-IDF values for first document:")
print(tfidf_dense[0])
```

**Word Embeddings:**
```python
# Using pre-trained word embeddings
from gensim.models import Word2Vec
import numpy as np

# Sample sentences for training
sentences = [
    ['the', 'cat', 'sat', 'on', 'mat'],
    ['the', 'dog', 'ran', 'in', 'park'],
    ['cats', 'and', 'dogs', 'are', 'pets'],
    ['love', 'my', 'pet', 'cat']
]

# Train Word2Vec model
model = Word2Vec(sentences, vector_size=100, window=5, min_count=1, workers=4)

# Get word vector
cat_vector = model.wv['cat']
print("Vector for 'cat':", cat_vector[:10])  # First 10 dimensions

# Find similar words
similar_words = model.wv.most_similar('cat', topn=3)
print("Words similar to 'cat':", similar_words)

# Word similarity
similarity = model.wv.similarity('cat', 'dog')
print(f"Similarity between 'cat' and 'dog': {similarity:.3f}")
```

---

## Advanced NLP

### 11. Sentiment Analysis

```python
from sklearn.model_selection import train_test_split
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import classification_report

# Sample sentiment analysis pipeline
class SentimentAnalyzer:
    def __init__(self):
        self.preprocessor = TextPreprocessor()
        self.vectorizer = TfidfVectorizer(max_features=5000, stop_words='english')
        self.classifier = LogisticRegression()
    
    def prepare_data(self, texts, labels):
        """Preprocess texts and create feature vectors"""
        # Preprocess texts
        processed_texts = [self.preprocessor.preprocess(text) for text in texts]
        
        # Create TF-IDF features
        X = self.vectorizer.fit_transform(processed_texts)
        
        return X, labels
    
    def train(self, texts, labels):
        """Train the sentiment classifier"""
        X, y = self.prepare_data(texts, labels)
        self.classifier.fit(X, y)
    
    def predict(self, texts):
        """Predict sentiment for new texts"""
        processed_texts = [self.preprocessor.preprocess(text) for text in texts]
        X = self.vectorizer.transform(processed_texts)
        predictions = self.classifier.predict(X)
        probabilities = self.classifier.predict_proba(X)
        return predictions, probabilities

# Example usage (with sample data)
sample_texts = [
    "I love this movie, it's amazing!",
    "This film is terrible, waste of time",
    "Great acting and storyline",
    "Boring and predictable plot"
]
sample_labels = [1, 0, 1, 0]  # 1: positive, 0: negative

analyzer = SentimentAnalyzer()
analyzer.train(sample_texts, sample_labels)

# Test prediction
test_texts = ["This movie is fantastic!", "I hate this film"]
predictions, probabilities = analyzer.predict(test_texts)
print("Predictions:", predictions)
print("Probabilities:", probabilities)
```

### 12. Named Entity Recognition (NER)

```python
import spacy

# Load pre-trained model
nlp = spacy.load("en_core_web_sm")

def extract_entities(text):
    """Extract named entities from text"""
    doc = nlp(text)
    
    entities = []
    for ent in doc.ents:
        entities.append({
            'text': ent.text,
            'label': ent.label_,
            'description': spacy.explain(ent.label_),
            'start': ent.start_char,
            'end': ent.end_char
        })
    
    return entities

# Example usage
sample_text = "Apple Inc. was founded by Steve Jobs in Cupertino, California in 1976."
entities = extract_entities(sample_text)

print("Named Entities:")
for entity in entities:
    print(f"- {entity['text']} ({entity['label']}): {entity['description']}")
```

---

## Common Interview Questions

### 13. Deep Learning Questions

**Q: What is the vanishing gradient problem?**
**A:** In deep networks, gradients become exponentially smaller as they propagate backward, making it difficult to train early layers. Solutions include:
- ReLU activation functions
- Batch normalization
- Residual connections (ResNet)
- LSTM/GRU for RNNs

**Q: Explain the difference between CNN and RNN.**
**A:**
- **CNN** - Spatial data (images), local connectivity, parameter sharing
- **RNN** - Sequential data (text, time series), temporal dependencies, memory

**Q: What is transfer learning?**
**A:** Using pre-trained models as starting point for new tasks. Benefits:
- Faster training
- Better performance with limited data
- Leverages learned features

### 14. NLP Questions

**Q: What is the difference between stemming and lemmatization?**
**A:**
- **Stemming** - Removes suffixes to get root form (running → run)
- **Lemmatization** - Reduces to dictionary form considering context (better → good)

**Q: Explain TF-IDF.**
**A:** 
- **TF** - Term frequency in document
- **IDF** - Inverse document frequency (rarity across corpus)
- **Purpose** - Identifies important words while reducing common word impact

**Q: What are attention mechanisms?**
**A:** Allow models to focus on relevant parts of input sequence. Used in:
- Machine translation
- Text summarization
- Transformer models (BERT, GPT)

### Key Interview Tips:
1. **Understand the intuition** behind architectures
2. **Know when to use** different models
3. **Explain preprocessing steps** and their importance
4. **Understand evaluation metrics** for different tasks
5. **Be familiar with popular frameworks** (TensorFlow, PyTorch)
6. **Know current trends** (Transformers, BERT, GPT)

### Practice Problems:
1. Implement a simple neural network from scratch
2. Build CNN for image classification
3. Create RNN for text generation
4. Implement sentiment analysis pipeline
5. Fine-tune pre-trained model for specific task

Remember: **Deep learning and NLP are rapidly evolving fields - focus on fundamentals and stay updated with latest developments!**
