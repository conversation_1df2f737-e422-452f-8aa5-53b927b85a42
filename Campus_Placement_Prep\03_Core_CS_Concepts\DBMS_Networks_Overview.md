# DBMS & Computer Networks - Quick Reference Guide

## Database Management Systems (DBMS)

### 1. DBMS Fundamentals

**What is DBMS?**
Software that manages databases, providing interface between users and database.

**Key Features:**
- Data independence
- Data integrity
- Concurrent access
- Security
- Backup and recovery

**DBMS vs File System:**
| DBMS | File System |
|------|-------------|
| Data redundancy controlled | Data redundancy |
| Data consistency | Inconsistency possible |
| Data sharing | Limited sharing |
| Security features | Limited security |
| Backup/Recovery | Manual backup |

### 2. Database Models

**Relational Model:**
- Data stored in tables (relations)
- Rows (tuples) and columns (attributes)
- Primary keys and foreign keys
- ACID properties

**ER Model Components:**
- **Entity** - Real-world object
- **Attribute** - Property of entity
- **Relationship** - Association between entities

### 3. Normalization

**Purpose:** Eliminate redundancy and dependency

**Normal Forms:**
- **1NF** - Atomic values, no repeating groups
- **2NF** - 1NF + no partial dependencies
- **3NF** - 2NF + no transitive dependencies
- **BCNF** - 3NF + every determinant is candidate key

**Example:**
```sql
-- Unnormalized
Student(ID, Name, Course1, Course2, Course3)

-- 1NF
Student(ID, Name)
StudentCourse(StudentID, Course)

-- 2NF & 3NF
Student(ID, Name)
Course(CourseID, CourseName, Instructor)
Enrollment(StudentID, CourseID, Grade)
```

### 4. SQL Essentials

**DDL (Data Definition Language):**
```sql
CREATE TABLE students (
    id INT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE
);

ALTER TABLE students ADD COLUMN age INT;
DROP TABLE students;
```

**DML (Data Manipulation Language):**
```sql
INSERT INTO students VALUES (1, 'John', '<EMAIL>', 20);
UPDATE students SET age = 21 WHERE id = 1;
DELETE FROM students WHERE id = 1;
```

**DQL (Data Query Language):**
```sql
-- Basic queries
SELECT name, age FROM students WHERE age > 18;

-- Joins
SELECT s.name, c.course_name 
FROM students s 
JOIN enrollments e ON s.id = e.student_id
JOIN courses c ON e.course_id = c.id;

-- Aggregation
SELECT department, COUNT(*), AVG(salary)
FROM employees 
GROUP BY department
HAVING COUNT(*) > 5;

-- Subqueries
SELECT name FROM students 
WHERE id IN (SELECT student_id FROM enrollments WHERE grade = 'A');
```

### 5. Transactions and ACID

**ACID Properties:**
- **Atomicity** - All or nothing
- **Consistency** - Valid state transitions
- **Isolation** - Concurrent transactions don't interfere
- **Durability** - Committed changes persist

**Transaction States:**
```
Active → Partially Committed → Committed
   ↓              ↓
Failed ← Aborted ←
```

**Concurrency Control:**
- **Locking** - Shared/Exclusive locks
- **Timestamp** - Ordering based on timestamps
- **Optimistic** - Validate before commit

### 6. Indexing

**Types of Indexes:**
- **Primary Index** - On primary key
- **Secondary Index** - On non-key attributes
- **Clustered** - Data physically ordered
- **Non-clustered** - Logical ordering

**B+ Tree Index:**
- Balanced tree structure
- All data in leaf nodes
- Efficient range queries
- O(log n) search time

---

## Computer Networks

### 1. Network Fundamentals

**What is Computer Network?**
Interconnected collection of autonomous computers that can communicate and share resources.

**Network Types:**
- **LAN** - Local Area Network (building/campus)
- **WAN** - Wide Area Network (cities/countries)
- **MAN** - Metropolitan Area Network (city)
- **PAN** - Personal Area Network (individual)

**Network Topologies:**
- **Bus** - Single communication line
- **Star** - Central hub/switch
- **Ring** - Circular connection
- **Mesh** - Multiple paths between nodes

### 2. OSI Model

**7 Layers:**
1. **Physical** - Bits transmission (cables, signals)
2. **Data Link** - Frame transmission (Ethernet, WiFi)
3. **Network** - Routing (IP, ICMP)
4. **Transport** - End-to-end delivery (TCP, UDP)
5. **Session** - Session management
6. **Presentation** - Data formatting, encryption
7. **Application** - User interface (HTTP, FTP, SMTP)

**TCP/IP Model:**
1. **Network Access** - Physical + Data Link
2. **Internet** - Network layer (IP)
3. **Transport** - TCP/UDP
4. **Application** - Session + Presentation + Application

### 3. IP Addressing

**IPv4 Address:**
- 32-bit address (4 octets)
- Classes A, B, C, D, E
- Private vs Public addresses

**Subnetting:**
```
Network: ***********/24
Subnet Mask: *************
Host Range: *********** - *************
Broadcast: *************
```

**IPv6:**
- 128-bit address
- Hexadecimal notation
- Solves IPv4 exhaustion

### 4. Transport Layer Protocols

**TCP (Transmission Control Protocol):**
- Connection-oriented
- Reliable delivery
- Flow control
- Congestion control
- Three-way handshake

**UDP (User Datagram Protocol):**
- Connectionless
- Unreliable but fast
- No flow control
- Minimal overhead

**TCP vs UDP:**
| Feature | TCP | UDP |
|---------|-----|-----|
| Connection | Connection-oriented | Connectionless |
| Reliability | Reliable | Unreliable |
| Speed | Slower | Faster |
| Overhead | High | Low |
| Use cases | Web, Email, FTP | Gaming, Streaming, DNS |

### 5. Application Layer Protocols

**HTTP/HTTPS:**
- Web communication
- Request-response model
- Methods: GET, POST, PUT, DELETE
- Status codes: 200, 404, 500, etc.

**DNS (Domain Name System):**
- Translates domain names to IP addresses
- Hierarchical structure
- Caching for performance

**SMTP/POP3/IMAP:**
- Email protocols
- SMTP for sending
- POP3/IMAP for receiving

**FTP:**
- File transfer protocol
- Control and data connections
- Active vs passive modes

### 6. Network Security

**Common Threats:**
- **Eavesdropping** - Intercepting data
- **Spoofing** - Impersonating another entity
- **DoS/DDoS** - Denial of Service attacks
- **Man-in-the-middle** - Intercepting communication

**Security Measures:**
- **Encryption** - Data protection
- **Firewalls** - Access control
- **VPN** - Secure tunneling
- **Authentication** - Identity verification
- **Digital certificates** - Trust establishment

### 7. Network Performance

**Key Metrics:**
- **Bandwidth** - Maximum data rate
- **Throughput** - Actual data rate
- **Latency** - Delay in transmission
- **Jitter** - Variation in latency
- **Packet Loss** - Lost data packets

**Performance Optimization:**
- Quality of Service (QoS)
- Traffic shaping
- Load balancing
- Caching
- Content Delivery Networks (CDN)

---

## Common Interview Questions

### DBMS Questions:

**Q: What is the difference between DELETE, DROP, and TRUNCATE?**
**A:**
- **DELETE** - Removes rows, can be rolled back, slower
- **DROP** - Removes entire table structure
- **TRUNCATE** - Removes all rows, faster than DELETE, cannot be rolled back

**Q: Explain different types of joins.**
**A:**
- **INNER JOIN** - Matching records from both tables
- **LEFT JOIN** - All records from left table
- **RIGHT JOIN** - All records from right table
- **FULL OUTER JOIN** - All records from both tables

**Q: What is database deadlock?**
**A:** Situation where two or more transactions wait for each other to release locks, creating a circular dependency.

### Network Questions:

**Q: What happens when you type a URL in browser?**
**A:**
1. DNS lookup to get IP address
2. TCP connection establishment
3. HTTP request sent
4. Server processes request
5. HTTP response received
6. Browser renders page

**Q: Difference between hub, switch, and router?**
**A:**
- **Hub** - Physical layer, broadcasts to all ports
- **Switch** - Data link layer, learns MAC addresses
- **Router** - Network layer, routes between networks

**Q: What is NAT?**
**A:** Network Address Translation - Maps private IP addresses to public IP addresses, allowing multiple devices to share single public IP.

### Key Interview Tips:

**For DBMS:**
1. Understand normalization thoroughly
2. Practice SQL queries (joins, subqueries, aggregations)
3. Know transaction concepts and ACID properties
4. Understand indexing and query optimization
5. Be familiar with NoSQL databases

**For Networks:**
1. Understand OSI and TCP/IP models
2. Know common protocols and their purposes
3. Understand IP addressing and subnetting
4. Be familiar with network security concepts
5. Know troubleshooting basics

### Practice Problems:

**DBMS:**
1. Design database schema for e-commerce system
2. Write complex SQL queries with multiple joins
3. Normalize given unnormalized tables
4. Explain query execution plans
5. Design for high availability and scalability

**Networks:**
1. Calculate subnet ranges and host counts
2. Trace packet flow through network layers
3. Design network topology for organization
4. Troubleshoot connectivity issues
5. Explain security implementations

Remember: **Both DBMS and Networks are foundational to modern computing - understand the principles, not just the syntax!**
