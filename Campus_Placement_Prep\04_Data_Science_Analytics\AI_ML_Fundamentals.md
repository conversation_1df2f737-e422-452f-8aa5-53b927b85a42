# AI/ML Fundamentals Interview Guide

## Table of Contents
1. [AI/ML Basics](#aiml-basics)
2. [Machine Learning Types](#machine-learning-types)
3. [Supervised Learning](#supervised-learning)
4. [Unsupervised Learning](#unsupervised-learning)
5. [Model Evaluation](#model-evaluation)
6. [Feature Engineering](#feature-engineering)
7. [Common Interview Questions](#common-interview-questions)

---

## AI/ML Basics

### 1. What is Artificial Intelligence and Machine Learning?

**Artificial Intelligence (AI):**
Simulation of human intelligence in machines that can think and act like humans.

**Machine Learning (ML):**
Subset of AI that enables machines to learn and improve from experience without being explicitly programmed.

**Deep Learning (DL):**
Subset of ML using neural networks with multiple layers to model complex patterns.

```
AI (Broad field)
├── Machine Learning
│   ├── Supervised Learning
│   ├── Unsupervised Learning
│   ├── Reinforcement Learning
│   └── Deep Learning
│       ├── Neural Networks
│       ├── CNNs
│       ├── RNNs
│       └── Transformers
```

**Real-world analogy:** AI is like human intelligence, ML is like learning from experience, and DL is like the complex neural networks in our brain.

### 2. Key Terminology

**Dataset:** Collection of data used for training
**Features:** Input variables (independent variables)
**Target/Label:** Output variable (dependent variable)
**Training:** Process of teaching the algorithm
**Prediction:** Output from trained model
**Model:** Mathematical representation learned from data

```python
# Basic ML workflow example
import pandas as pd
from sklearn.model_selection import train_test_split
from sklearn.linear_model import LinearRegression
from sklearn.metrics import mean_squared_error

# 1. Load and prepare data
data = pd.read_csv('house_prices.csv')
X = data[['size', 'bedrooms', 'location_score']]  # Features
y = data['price']  # Target

# 2. Split data
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

# 3. Train model
model = LinearRegression()
model.fit(X_train, y_train)

# 4. Make predictions
predictions = model.predict(X_test)

# 5. Evaluate
mse = mean_squared_error(y_test, predictions)
print(f"Mean Squared Error: {mse}")
```

---

## Machine Learning Types

### 3. Supervised Learning

**Definition:** Learning with labeled examples (input-output pairs).

**Types:**
- **Classification** - Predicting categories/classes
- **Regression** - Predicting continuous values

**Common Algorithms:**

**Linear Regression:**
```python
# Simple linear regression example
import numpy as np
import matplotlib.pyplot as plt
from sklearn.linear_model import LinearRegression

# Generate sample data
np.random.seed(42)
X = np.random.randn(100, 1)
y = 2 * X.flatten() + 1 + 0.1 * np.random.randn(100)

# Train model
model = LinearRegression()
model.fit(X, y)

# Get parameters
print(f"Slope (coefficient): {model.coef_[0]:.2f}")
print(f"Intercept: {model.intercept_:.2f}")

# Make predictions
X_new = np.array([[0], [1], [2]])
predictions = model.predict(X_new)
print(f"Predictions: {predictions}")
```

**Logistic Regression:**
```python
from sklearn.linear_model import LogisticRegression
from sklearn.datasets import make_classification

# Generate binary classification data
X, y = make_classification(n_samples=1000, n_features=2, n_redundant=0, 
                          n_informative=2, n_clusters_per_class=1, random_state=42)

# Train logistic regression
log_reg = LogisticRegression()
log_reg.fit(X, y)

# Predict probabilities
probabilities = log_reg.predict_proba(X[:5])
print("Probabilities for first 5 samples:")
print(probabilities)

# Predict classes
predictions = log_reg.predict(X[:5])
print(f"Predicted classes: {predictions}")
```

**Decision Trees:**
```python
from sklearn.tree import DecisionTreeClassifier
from sklearn.datasets import load_iris

# Load iris dataset
iris = load_iris()
X, y = iris.data, iris.target

# Train decision tree
dt = DecisionTreeClassifier(max_depth=3, random_state=42)
dt.fit(X, y)

# Feature importance
feature_importance = dt.feature_importances_
feature_names = iris.feature_names

print("Feature Importance:")
for name, importance in zip(feature_names, feature_importance):
    print(f"{name}: {importance:.3f}")
```

### 4. Unsupervised Learning

**Definition:** Learning patterns from data without labeled examples.

**Types:**
- **Clustering** - Grouping similar data points
- **Dimensionality Reduction** - Reducing number of features
- **Association Rules** - Finding relationships between variables

**K-Means Clustering:**
```python
from sklearn.cluster import KMeans
from sklearn.datasets import make_blobs
import matplotlib.pyplot as plt

# Generate sample data
X, _ = make_blobs(n_samples=300, centers=4, cluster_std=0.60, random_state=0)

# Apply K-means
kmeans = KMeans(n_clusters=4, random_state=42)
cluster_labels = kmeans.fit_predict(X)

# Get cluster centers
centers = kmeans.cluster_centers_

print(f"Cluster centers:\n{centers}")
print(f"Inertia (within-cluster sum of squares): {kmeans.inertia_}")

# Plot results (conceptual)
plt.scatter(X[:, 0], X[:, 1], c=cluster_labels, cmap='viridis')
plt.scatter(centers[:, 0], centers[:, 1], c='red', marker='x', s=200)
plt.title('K-Means Clustering')
plt.show()
```

**Principal Component Analysis (PCA):**
```python
from sklearn.decomposition import PCA
from sklearn.datasets import load_digits

# Load digits dataset
digits = load_digits()
X = digits.data  # 64 features (8x8 pixel images)

# Apply PCA
pca = PCA(n_components=2)  # Reduce to 2 dimensions
X_reduced = pca.fit_transform(X)

# Explained variance ratio
print(f"Explained variance ratio: {pca.explained_variance_ratio_}")
print(f"Total variance explained: {sum(pca.explained_variance_ratio_):.3f}")

# Original vs reduced dimensions
print(f"Original shape: {X.shape}")
print(f"Reduced shape: {X_reduced.shape}")
```

---

## Supervised Learning

### 5. Classification Algorithms

**Support Vector Machine (SVM):**
```python
from sklearn.svm import SVC
from sklearn.datasets import make_classification
from sklearn.preprocessing import StandardScaler

# Generate data
X, y = make_classification(n_samples=1000, n_features=2, n_redundant=0, 
                          n_informative=2, random_state=42)

# Scale features (important for SVM)
scaler = StandardScaler()
X_scaled = scaler.fit_transform(X)

# Train SVM with different kernels
svm_linear = SVC(kernel='linear', random_state=42)
svm_rbf = SVC(kernel='rbf', random_state=42)

svm_linear.fit(X_scaled, y)
svm_rbf.fit(X_scaled, y)

# Compare accuracy
linear_score = svm_linear.score(X_scaled, y)
rbf_score = svm_rbf.score(X_scaled, y)

print(f"Linear SVM accuracy: {linear_score:.3f}")
print(f"RBF SVM accuracy: {rbf_score:.3f}")
```

**Random Forest:**
```python
from sklearn.ensemble import RandomForestClassifier
from sklearn.datasets import load_wine

# Load wine dataset
wine = load_wine()
X, y = wine.data, wine.target

# Train Random Forest
rf = RandomForestClassifier(n_estimators=100, random_state=42)
rf.fit(X, y)

# Feature importance
feature_importance = rf.feature_importances_
feature_names = wine.feature_names

# Sort features by importance
importance_df = pd.DataFrame({
    'feature': feature_names,
    'importance': feature_importance
}).sort_values('importance', ascending=False)

print("Top 5 most important features:")
print(importance_df.head())

# Out-of-bag score (built-in cross-validation)
rf_oob = RandomForestClassifier(n_estimators=100, oob_score=True, random_state=42)
rf_oob.fit(X, y)
print(f"Out-of-bag score: {rf_oob.oob_score_:.3f}")
```

### 6. Regression Algorithms

**Polynomial Regression:**
```python
from sklearn.preprocessing import PolynomialFeatures
from sklearn.pipeline import Pipeline
import numpy as np

# Generate non-linear data
np.random.seed(42)
X = np.linspace(0, 1, 100).reshape(-1, 1)
y = 1.5 * X.flatten() + 0.5 * (X.flatten() ** 2) + 0.1 * np.random.randn(100)

# Create polynomial features and fit
poly_reg = Pipeline([
    ('poly', PolynomialFeatures(degree=2)),
    ('linear', LinearRegression())
])

poly_reg.fit(X, y)

# Compare with linear regression
linear_reg = LinearRegression()
linear_reg.fit(X, y)

# Predictions
X_test = np.linspace(0, 1, 20).reshape(-1, 1)
poly_pred = poly_reg.predict(X_test)
linear_pred = linear_reg.predict(X_test)

# Calculate R² scores
poly_score = poly_reg.score(X, y)
linear_score = linear_reg.score(X, y)

print(f"Polynomial regression R²: {poly_score:.3f}")
print(f"Linear regression R²: {linear_score:.3f}")
```

**Ridge and Lasso Regression:**
```python
from sklearn.linear_model import Ridge, Lasso
from sklearn.datasets import make_regression

# Generate data with noise
X, y = make_regression(n_samples=100, n_features=10, noise=0.1, random_state=42)

# Ridge regression (L2 regularization)
ridge = Ridge(alpha=1.0)
ridge.fit(X, y)

# Lasso regression (L1 regularization)
lasso = Lasso(alpha=0.1)
lasso.fit(X, y)

# Compare coefficients
print("Ridge coefficients:", ridge.coef_[:5])  # First 5 coefficients
print("Lasso coefficients:", lasso.coef_[:5])

# Lasso can set coefficients to zero (feature selection)
non_zero_features = np.sum(lasso.coef_ != 0)
print(f"Lasso selected {non_zero_features} out of {len(lasso.coef_)} features")
```

---

## Model Evaluation

### 7. Evaluation Metrics

**Classification Metrics:**
```python
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
from sklearn.metrics import confusion_matrix, classification_report
from sklearn.model_selection import cross_val_score

# Example predictions
y_true = [0, 1, 1, 0, 1, 1, 0, 0, 1, 0]
y_pred = [0, 1, 1, 0, 0, 1, 0, 1, 1, 0]

# Calculate metrics
accuracy = accuracy_score(y_true, y_pred)
precision = precision_score(y_true, y_pred)
recall = recall_score(y_true, y_pred)
f1 = f1_score(y_true, y_pred)

print(f"Accuracy: {accuracy:.3f}")
print(f"Precision: {precision:.3f}")
print(f"Recall: {recall:.3f}")
print(f"F1-score: {f1:.3f}")

# Confusion matrix
cm = confusion_matrix(y_true, y_pred)
print(f"Confusion Matrix:\n{cm}")

# Classification report
print("\nClassification Report:")
print(classification_report(y_true, y_pred))
```

**Regression Metrics:**
```python
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import numpy as np

# Example predictions
y_true = [3, -0.5, 2, 7, 4.2]
y_pred = [2.5, 0.0, 2, 8, 4.1]

# Calculate metrics
mse = mean_squared_error(y_true, y_pred)
rmse = np.sqrt(mse)
mae = mean_absolute_error(y_true, y_pred)
r2 = r2_score(y_true, y_pred)

print(f"Mean Squared Error: {mse:.3f}")
print(f"Root Mean Squared Error: {rmse:.3f}")
print(f"Mean Absolute Error: {mae:.3f}")
print(f"R² Score: {r2:.3f}")
```

**Cross-Validation:**
```python
from sklearn.model_selection import cross_val_score, StratifiedKFold

# Load dataset
iris = load_iris()
X, y = iris.data, iris.target

# Model
rf = RandomForestClassifier(n_estimators=100, random_state=42)

# 5-fold cross-validation
cv_scores = cross_val_score(rf, X, y, cv=5)

print(f"Cross-validation scores: {cv_scores}")
print(f"Mean CV score: {cv_scores.mean():.3f} (+/- {cv_scores.std() * 2:.3f})")

# Stratified K-Fold (maintains class distribution)
skf = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
stratified_scores = cross_val_score(rf, X, y, cv=skf)

print(f"Stratified CV scores: {stratified_scores}")
print(f"Mean Stratified CV score: {stratified_scores.mean():.3f}")
```

---

## Feature Engineering

### 8. Data Preprocessing

**Handling Missing Data:**
```python
import pandas as pd
import numpy as np
from sklearn.impute import SimpleImputer

# Create sample data with missing values
data = pd.DataFrame({
    'age': [25, 30, np.nan, 35, 28],
    'income': [50000, np.nan, 60000, 75000, 55000],
    'education': ['Bachelor', 'Master', 'Bachelor', np.nan, 'PhD']
})

print("Original data:")
print(data)

# Strategy 1: Drop rows with missing values
data_dropped = data.dropna()
print(f"\nAfter dropping rows: {data_dropped.shape}")

# Strategy 2: Fill with mean/mode
data_filled = data.copy()
data_filled['age'].fillna(data_filled['age'].mean(), inplace=True)
data_filled['income'].fillna(data_filled['income'].median(), inplace=True)
data_filled['education'].fillna(data_filled['education'].mode()[0], inplace=True)

print("\nAfter filling missing values:")
print(data_filled)

# Strategy 3: Using sklearn imputer
imputer = SimpleImputer(strategy='mean')
numerical_cols = ['age', 'income']
data[numerical_cols] = imputer.fit_transform(data[numerical_cols])
```

**Feature Scaling:**
```python
from sklearn.preprocessing import StandardScaler, MinMaxScaler, RobustScaler

# Sample data
data = np.array([[1, 2], [3, 4], [5, 6], [100, 200]])

# Standard Scaler (z-score normalization)
standard_scaler = StandardScaler()
data_standard = standard_scaler.fit_transform(data)

# Min-Max Scaler (0-1 normalization)
minmax_scaler = MinMaxScaler()
data_minmax = minmax_scaler.fit_transform(data)

# Robust Scaler (uses median and IQR)
robust_scaler = RobustScaler()
data_robust = robust_scaler.fit_transform(data)

print("Original data:")
print(data)
print("\nStandardized:")
print(data_standard)
print("\nMin-Max scaled:")
print(data_minmax)
print("\nRobust scaled:")
print(data_robust)
```

**Encoding Categorical Variables:**
```python
from sklearn.preprocessing import LabelEncoder, OneHotEncoder
import pandas as pd

# Sample categorical data
data = pd.DataFrame({
    'color': ['red', 'blue', 'green', 'red', 'blue'],
    'size': ['small', 'medium', 'large', 'medium', 'small'],
    'price': [10, 15, 20, 12, 8]
})

# Label Encoding (ordinal)
label_encoder = LabelEncoder()
data['size_encoded'] = label_encoder.fit_transform(data['size'])

# One-Hot Encoding (nominal)
data_encoded = pd.get_dummies(data, columns=['color'], prefix='color')

print("Original data:")
print(data)
print("\nAfter encoding:")
print(data_encoded)

# Manual one-hot encoding with sklearn
from sklearn.preprocessing import OneHotEncoder

ohe = OneHotEncoder(sparse=False, drop='first')  # drop='first' to avoid multicollinearity
color_encoded = ohe.fit_transform(data[['color']])
color_feature_names = ohe.get_feature_names_out(['color'])

print(f"\nOne-hot encoded colors:")
print(color_encoded)
print(f"Feature names: {color_feature_names}")
```

---

## Common Interview Questions

### 9. Fundamental ML Questions

**Q: What is the bias-variance tradeoff?**
**A:** 
- **Bias** - Error from oversimplifying the model (underfitting)
- **Variance** - Error from sensitivity to small fluctuations (overfitting)
- **Tradeoff** - Reducing one typically increases the other
- **Goal** - Find optimal balance for minimum total error

**Q: Explain overfitting and how to prevent it.**
**A:**
**Overfitting** - Model learns training data too well, poor generalization
**Prevention methods:**
- Cross-validation
- Regularization (L1/L2)
- Early stopping
- Dropout (neural networks)
- More training data
- Feature selection

**Q: What is the curse of dimensionality?**
**A:** As number of features increases, data becomes sparse in high-dimensional space, making it harder to find patterns. Solutions include dimensionality reduction (PCA, feature selection).

**Q: Difference between bagging and boosting?**
**A:**
- **Bagging** - Train multiple models in parallel, average predictions (Random Forest)
- **Boosting** - Train models sequentially, each corrects previous errors (AdaBoost, XGBoost)

### 10. Algorithm-Specific Questions

**Q: When to use logistic regression vs SVM?**
**A:**
- **Logistic Regression** - Linear relationships, need probability estimates, interpretability
- **SVM** - Non-linear relationships (with kernels), high-dimensional data, robust to outliers

**Q: How does Random Forest work?**
**A:**
1. Bootstrap sampling of training data
2. Train decision trees on each sample
3. Random feature selection at each split
4. Aggregate predictions (voting/averaging)
5. Reduces overfitting through ensemble

**Q: Explain K-means clustering algorithm.**
**A:**
1. Choose number of clusters (k)
2. Initialize cluster centroids randomly
3. Assign each point to nearest centroid
4. Update centroids to mean of assigned points
5. Repeat steps 3-4 until convergence

### Key Interview Tips:
1. **Understand the intuition** behind algorithms
2. **Know when to use** which algorithm
3. **Explain trade-offs** clearly
4. **Practice implementing** algorithms from scratch
5. **Understand evaluation metrics** and their use cases
6. **Be familiar with real-world applications**

### Practice Problems:
1. Implement linear regression from scratch
2. Build a complete ML pipeline (data preprocessing to evaluation)
3. Compare different algorithms on same dataset
4. Explain feature selection techniques
5. Discuss handling imbalanced datasets

Remember: **Focus on understanding concepts and their practical applications rather than just memorizing formulas!**
