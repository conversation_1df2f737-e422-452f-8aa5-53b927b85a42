# Operating Systems Interview Guide

## Table of Contents
1. [OS Fundamentals](#os-fundamentals)
2. [Process Management](#process-management)
3. [Memory Management](#memory-management)
4. [File Systems](#file-systems)
5. [Synchronization](#synchronization)
6. [Deadlocks](#deadlocks)
7. [Common Interview Questions](#common-interview-questions)

---

## OS Fundamentals

### 1. What is an Operating System?

**Answer:**
An Operating System is system software that manages computer hardware and software resources and provides common services for computer programs.

**Key Functions:**
- **Process Management** - Creating, scheduling, and terminating processes
- **Memory Management** - Allocating and deallocating memory
- **File System Management** - Managing files and directories
- **I/O Management** - Handling input/output operations
- **Security** - Protecting system resources

**Real-world analogy:** OS is like a restaurant manager - coordinates between customers (applications), waiters (processes), kitchen (CPU), and storage (memory/disk).

### 2. Types of Operating Systems

**Batch OS:**
- Jobs are processed in batches without user interaction
- Example: Early mainframe systems

**Time-Sharing OS:**
- Multiple users share system resources simultaneously
- Example: UNIX, Linux

**Real-Time OS:**
- Guarantees response within specific time constraints
- Example: Embedded systems, medical devices

**Distributed OS:**
- Manages resources across multiple machines
- Example: Google's distributed systems

**Mobile OS:**
- Optimized for mobile devices
- Example: Android, iOS

---

## Process Management

### 3. Process vs Thread

**Process:**
- Independent execution unit with its own memory space
- Heavy-weight entity
- Inter-process communication is expensive
- Crash of one process doesn't affect others

**Thread:**
- Lightweight execution unit within a process
- Shares memory space with other threads in same process
- Communication is faster (shared memory)
- Crash of one thread can affect entire process

```python
# Process example (conceptual)
import multiprocessing
import os

def worker_process(name):
    print(f"Process {name} with PID: {os.getpid()}")
    # Each process has its own memory space

if __name__ == "__main__":
    processes = []
    for i in range(3):
        p = multiprocessing.Process(target=worker_process, args=(f"Worker-{i}",))
        processes.append(p)
        p.start()
    
    for p in processes:
        p.join()

# Thread example (conceptual)
import threading

shared_data = 0  # Shared between threads
lock = threading.Lock()

def worker_thread(name):
    global shared_data
    with lock:
        shared_data += 1
        print(f"Thread {name}: shared_data = {shared_data}")

threads = []
for i in range(3):
    t = threading.Thread(target=worker_thread, args=(f"Worker-{i}",))
    threads.append(t)
    t.start()

for t in threads:
    t.join()
```

### 4. Process States and Lifecycle

**Process States:**
1. **New** - Process is being created
2. **Ready** - Process is waiting to be assigned to CPU
3. **Running** - Process is being executed
4. **Waiting/Blocked** - Process is waiting for I/O or event
5. **Terminated** - Process has finished execution

```
    New → Ready → Running → Terminated
           ↑        ↓
           ←─ Waiting/Blocked
```

**State Transitions:**
- **New → Ready**: Process creation completed
- **Ready → Running**: CPU scheduler selects process
- **Running → Ready**: Time quantum expired (preemption)
- **Running → Waiting**: Process requests I/O
- **Waiting → Ready**: I/O completion
- **Running → Terminated**: Process completes

### 5. CPU Scheduling Algorithms

**First Come First Serve (FCFS):**
```python
def fcfs_scheduling(processes):
    """
    processes: list of (arrival_time, burst_time)
    """
    processes.sort(key=lambda x: x[0])  # Sort by arrival time
    
    current_time = 0
    waiting_times = []
    turnaround_times = []
    
    for arrival, burst in processes:
        if current_time < arrival:
            current_time = arrival
        
        waiting_time = current_time - arrival
        waiting_times.append(waiting_time)
        
        current_time += burst
        turnaround_time = current_time - arrival
        turnaround_times.append(turnaround_time)
    
    avg_waiting = sum(waiting_times) / len(waiting_times)
    avg_turnaround = sum(turnaround_times) / len(turnaround_times)
    
    return avg_waiting, avg_turnaround

# Example
processes = [(0, 5), (1, 3), (2, 8), (3, 6)]  # (arrival, burst)
avg_wt, avg_tat = fcfs_scheduling(processes)
print(f"Average Waiting Time: {avg_wt}")
print(f"Average Turnaround Time: {avg_tat}")
```

**Shortest Job First (SJF):**
```python
def sjf_scheduling(processes):
    """Non-preemptive SJF"""
    processes.sort(key=lambda x: (x[0], x[1]))  # Sort by arrival, then burst
    
    current_time = 0
    completed = []
    remaining = processes.copy()
    
    while remaining:
        # Find processes that have arrived
        available = [p for p in remaining if p[0] <= current_time]
        
        if not available:
            current_time = min(remaining, key=lambda x: x[0])[0]
            continue
        
        # Select shortest job among available
        selected = min(available, key=lambda x: x[1])
        remaining.remove(selected)
        
        arrival, burst = selected
        waiting_time = current_time - arrival
        current_time += burst
        turnaround_time = current_time - arrival
        
        completed.append((waiting_time, turnaround_time))
    
    avg_waiting = sum(wt for wt, _ in completed) / len(completed)
    avg_turnaround = sum(tat for _, tat in completed) / len(completed)
    
    return avg_waiting, avg_turnaround
```

**Round Robin:**
```python
def round_robin_scheduling(processes, time_quantum):
    """
    processes: list of (arrival_time, burst_time)
    time_quantum: time slice for each process
    """
    from collections import deque
    
    # Initialize process data
    process_data = []
    for i, (arrival, burst) in enumerate(processes):
        process_data.append({
            'id': i,
            'arrival': arrival,
            'burst': burst,
            'remaining': burst,
            'waiting': 0,
            'turnaround': 0,
            'completed': False
        })
    
    ready_queue = deque()
    current_time = 0
    completed_count = 0
    
    while completed_count < len(processes):
        # Add newly arrived processes to ready queue
        for process in process_data:
            if (process['arrival'] <= current_time and 
                not process['completed'] and 
                process not in ready_queue):
                ready_queue.append(process)
        
        if not ready_queue:
            current_time += 1
            continue
        
        # Execute current process
        current_process = ready_queue.popleft()
        execution_time = min(time_quantum, current_process['remaining'])
        
        current_time += execution_time
        current_process['remaining'] -= execution_time
        
        # Check if process completed
        if current_process['remaining'] == 0:
            current_process['completed'] = True
            current_process['turnaround'] = current_time - current_process['arrival']
            current_process['waiting'] = current_process['turnaround'] - current_process['burst']
            completed_count += 1
        else:
            # Add back to ready queue if not completed
            ready_queue.append(current_process)
    
    avg_waiting = sum(p['waiting'] for p in process_data) / len(process_data)
    avg_turnaround = sum(p['turnaround'] for p in process_data) / len(process_data)
    
    return avg_waiting, avg_turnaround
```

---

## Memory Management

### 6. Memory Allocation Techniques

**Contiguous Allocation:**
- **First Fit** - Allocate first available block that fits
- **Best Fit** - Allocate smallest block that fits
- **Worst Fit** - Allocate largest available block

```python
class MemoryManager:
    def __init__(self, total_memory):
        self.memory_blocks = [{'start': 0, 'size': total_memory, 'allocated': False}]
    
    def first_fit(self, process_size):
        """First Fit allocation"""
        for i, block in enumerate(self.memory_blocks):
            if not block['allocated'] and block['size'] >= process_size:
                # Split block if necessary
                if block['size'] > process_size:
                    new_block = {
                        'start': block['start'] + process_size,
                        'size': block['size'] - process_size,
                        'allocated': False
                    }
                    self.memory_blocks.insert(i + 1, new_block)
                
                block['size'] = process_size
                block['allocated'] = True
                return block['start']
        
        return -1  # No suitable block found
    
    def best_fit(self, process_size):
        """Best Fit allocation"""
        best_block = None
        best_index = -1
        
        for i, block in enumerate(self.memory_blocks):
            if (not block['allocated'] and 
                block['size'] >= process_size and
                (best_block is None or block['size'] < best_block['size'])):
                best_block = block
                best_index = i
        
        if best_block:
            # Split block if necessary
            if best_block['size'] > process_size:
                new_block = {
                    'start': best_block['start'] + process_size,
                    'size': best_block['size'] - process_size,
                    'allocated': False
                }
                self.memory_blocks.insert(best_index + 1, new_block)
            
            best_block['size'] = process_size
            best_block['allocated'] = True
            return best_block['start']
        
        return -1
    
    def deallocate(self, start_address):
        """Deallocate memory block"""
        for block in self.memory_blocks:
            if block['start'] == start_address and block['allocated']:
                block['allocated'] = False
                self._merge_free_blocks()
                return True
        return False
    
    def _merge_free_blocks(self):
        """Merge adjacent free blocks"""
        i = 0
        while i < len(self.memory_blocks) - 1:
            current = self.memory_blocks[i]
            next_block = self.memory_blocks[i + 1]
            
            if (not current['allocated'] and 
                not next_block['allocated'] and
                current['start'] + current['size'] == next_block['start']):
                current['size'] += next_block['size']
                self.memory_blocks.pop(i + 1)
            else:
                i += 1
```

### 7. Virtual Memory and Paging

**Paging Concepts:**
- **Page** - Fixed-size block of virtual memory
- **Frame** - Fixed-size block of physical memory
- **Page Table** - Maps virtual pages to physical frames

```python
class PageTable:
    def __init__(self, num_pages):
        self.pages = {}  # page_number -> frame_number
        self.valid_bits = {}  # page_number -> boolean
        self.reference_bits = {}  # For LRU
        self.modify_bits = {}  # Dirty bits
    
    def add_page(self, page_num, frame_num):
        self.pages[page_num] = frame_num
        self.valid_bits[page_num] = True
        self.reference_bits[page_num] = True
        self.modify_bits[page_num] = False
    
    def get_frame(self, page_num):
        if page_num in self.pages and self.valid_bits[page_num]:
            self.reference_bits[page_num] = True
            return self.pages[page_num]
        return None  # Page fault
    
    def invalidate_page(self, page_num):
        if page_num in self.valid_bits:
            self.valid_bits[page_num] = False

class PageReplacementAlgorithm:
    def __init__(self, num_frames):
        self.num_frames = num_frames
        self.frames = []
        self.page_faults = 0
    
    def fifo_replacement(self, page_sequence):
        """First In First Out page replacement"""
        self.frames = []
        self.page_faults = 0
        
        for page in page_sequence:
            if page not in self.frames:
                self.page_faults += 1
                if len(self.frames) < self.num_frames:
                    self.frames.append(page)
                else:
                    self.frames.pop(0)  # Remove oldest
                    self.frames.append(page)
        
        return self.page_faults
    
    def lru_replacement(self, page_sequence):
        """Least Recently Used page replacement"""
        from collections import OrderedDict
        
        frames = OrderedDict()
        page_faults = 0
        
        for page in page_sequence:
            if page in frames:
                # Move to end (most recently used)
                frames.move_to_end(page)
            else:
                page_faults += 1
                if len(frames) < self.num_frames:
                    frames[page] = True
                else:
                    # Remove least recently used (first item)
                    frames.popitem(last=False)
                    frames[page] = True
        
        return page_faults
    
    def optimal_replacement(self, page_sequence):
        """Optimal page replacement (for comparison)"""
        frames = []
        page_faults = 0
        
        for i, page in enumerate(page_sequence):
            if page not in frames:
                page_faults += 1
                if len(frames) < self.num_frames:
                    frames.append(page)
                else:
                    # Find page that will be used farthest in future
                    future_uses = {}
                    for frame_page in frames:
                        try:
                            future_uses[frame_page] = page_sequence[i+1:].index(frame_page)
                        except ValueError:
                            future_uses[frame_page] = float('inf')
                    
                    # Replace page with farthest future use
                    page_to_replace = max(future_uses, key=future_uses.get)
                    frames[frames.index(page_to_replace)] = page
        
        return page_faults

# Example usage
pra = PageReplacementAlgorithm(3)
pages = [1, 2, 3, 4, 1, 2, 5, 1, 2, 3, 4, 5]

fifo_faults = pra.fifo_replacement(pages)
lru_faults = pra.lru_replacement(pages)
optimal_faults = pra.optimal_replacement(pages)

print(f"FIFO Page Faults: {fifo_faults}")
print(f"LRU Page Faults: {lru_faults}")
print(f"Optimal Page Faults: {optimal_faults}")
```

---

## File Systems

### 8. File System Structure

**File Allocation Methods:**

**Contiguous Allocation:**
- Files stored in contiguous blocks
- Fast sequential access
- External fragmentation problem

**Linked Allocation:**
- Files stored as linked list of blocks
- No external fragmentation
- Poor random access performance

**Indexed Allocation:**
- Index block contains pointers to data blocks
- Good random access
- Overhead of index blocks

```python
class FileSystem:
    def __init__(self, total_blocks):
        self.total_blocks = total_blocks
        self.free_blocks = set(range(total_blocks))
        self.files = {}  # filename -> file_info
    
    def create_file_contiguous(self, filename, size):
        """Create file using contiguous allocation"""
        if size > len(self.free_blocks):
            return False
        
        # Find contiguous free blocks
        sorted_free = sorted(self.free_blocks)
        start_block = None
        
        for i in range(len(sorted_free) - size + 1):
            if all(sorted_free[i] + j in self.free_blocks for j in range(size)):
                start_block = sorted_free[i]
                break
        
        if start_block is None:
            return False
        
        # Allocate blocks
        allocated_blocks = list(range(start_block, start_block + size))
        for block in allocated_blocks:
            self.free_blocks.remove(block)
        
        self.files[filename] = {
            'type': 'contiguous',
            'start_block': start_block,
            'size': size,
            'blocks': allocated_blocks
        }
        return True
    
    def create_file_linked(self, filename, size):
        """Create file using linked allocation"""
        if size > len(self.free_blocks):
            return False
        
        allocated_blocks = []
        for _ in range(size):
            if not self.free_blocks:
                # Rollback allocation
                for block in allocated_blocks:
                    self.free_blocks.add(block)
                return False
            
            block = self.free_blocks.pop()
            allocated_blocks.append(block)
        
        self.files[filename] = {
            'type': 'linked',
            'blocks': allocated_blocks,
            'size': size
        }
        return True
    
    def create_file_indexed(self, filename, size):
        """Create file using indexed allocation"""
        if size + 1 > len(self.free_blocks):  # +1 for index block
            return False
        
        # Allocate index block
        index_block = self.free_blocks.pop()
        
        # Allocate data blocks
        data_blocks = []
        for _ in range(size):
            if not self.free_blocks:
                # Rollback
                self.free_blocks.add(index_block)
                for block in data_blocks:
                    self.free_blocks.add(block)
                return False
            
            block = self.free_blocks.pop()
            data_blocks.append(block)
        
        self.files[filename] = {
            'type': 'indexed',
            'index_block': index_block,
            'data_blocks': data_blocks,
            'size': size
        }
        return True
    
    def delete_file(self, filename):
        """Delete file and free blocks"""
        if filename not in self.files:
            return False
        
        file_info = self.files[filename]
        
        if file_info['type'] == 'contiguous':
            for block in file_info['blocks']:
                self.free_blocks.add(block)
        elif file_info['type'] == 'linked':
            for block in file_info['blocks']:
                self.free_blocks.add(block)
        elif file_info['type'] == 'indexed':
            self.free_blocks.add(file_info['index_block'])
            for block in file_info['data_blocks']:
                self.free_blocks.add(block)
        
        del self.files[filename]
        return True
```

---

## Synchronization

### 9. Process Synchronization Problems

**Producer-Consumer Problem:**
```python
import threading
import time
import random
from queue import Queue

class ProducerConsumer:
    def __init__(self, buffer_size):
        self.buffer = Queue(maxsize=buffer_size)
        self.mutex = threading.Lock()
        self.not_full = threading.Condition(self.mutex)
        self.not_empty = threading.Condition(self.mutex)
        self.running = True
    
    def producer(self, producer_id):
        while self.running:
            item = random.randint(1, 100)
            
            with self.not_full:
                while self.buffer.full() and self.running:
                    self.not_full.wait()
                
                if self.running:
                    self.buffer.put(item)
                    print(f"Producer {producer_id} produced: {item}")
                    self.not_empty.notify()
            
            time.sleep(random.uniform(0.1, 0.5))
    
    def consumer(self, consumer_id):
        while self.running:
            with self.not_empty:
                while self.buffer.empty() and self.running:
                    self.not_empty.wait()
                
                if self.running and not self.buffer.empty():
                    item = self.buffer.get()
                    print(f"Consumer {consumer_id} consumed: {item}")
                    self.not_full.notify()
            
            time.sleep(random.uniform(0.1, 0.5))
    
    def stop(self):
        self.running = False
        with self.mutex:
            self.not_full.notify_all()
            self.not_empty.notify_all()

# Usage
pc = ProducerConsumer(5)

# Create threads
producers = [threading.Thread(target=pc.producer, args=(i,)) for i in range(2)]
consumers = [threading.Thread(target=pc.consumer, args=(i,)) for i in range(2)]

# Start threads
for p in producers:
    p.start()
for c in consumers:
    c.start()

# Run for a while then stop
time.sleep(5)
pc.stop()

# Wait for threads to finish
for p in producers:
    p.join()
for c in consumers:
    c.join()
```

**Readers-Writers Problem:**
```python
import threading
import time
import random

class ReadersWriters:
    def __init__(self):
        self.readers_count = 0
        self.readers_mutex = threading.Lock()
        self.writers_mutex = threading.Lock()
        self.shared_data = 0
    
    def reader(self, reader_id):
        with self.readers_mutex:
            self.readers_count += 1
            if self.readers_count == 1:
                self.writers_mutex.acquire()  # First reader locks writers
        
        # Reading section
        print(f"Reader {reader_id} reading: {self.shared_data}")
        time.sleep(random.uniform(0.1, 0.3))
        
        with self.readers_mutex:
            self.readers_count -= 1
            if self.readers_count == 0:
                self.writers_mutex.release()  # Last reader unlocks writers
    
    def writer(self, writer_id):
        with self.writers_mutex:
            # Writing section
            old_value = self.shared_data
            self.shared_data += 1
            print(f"Writer {writer_id} updated data: {old_value} -> {self.shared_data}")
            time.sleep(random.uniform(0.1, 0.3))

# Usage
rw = ReadersWriters()

readers = [threading.Thread(target=rw.reader, args=(i,)) for i in range(5)]
writers = [threading.Thread(target=rw.writer, args=(i,)) for i in range(2)]

for r in readers:
    r.start()
for w in writers:
    w.start()

for r in readers:
    r.join()
for w in writers:
    w.join()
```

---

## Deadlocks

### 10. Deadlock Detection and Prevention

**Banker's Algorithm (Deadlock Avoidance):**
```python
class BankersAlgorithm:
    def __init__(self, allocation, max_need, available):
        self.allocation = allocation  # Currently allocated resources
        self.max_need = max_need     # Maximum resources needed
        self.available = available   # Available resources
        self.num_processes = len(allocation)
        self.num_resources = len(available)
        
        # Calculate need matrix
        self.need = []
        for i in range(self.num_processes):
            need_row = []
            for j in range(self.num_resources):
                need_row.append(max_need[i][j] - allocation[i][j])
            self.need.append(need_row)
    
    def is_safe_state(self):
        """Check if current state is safe"""
        work = self.available.copy()
        finish = [False] * self.num_processes
        safe_sequence = []
        
        while len(safe_sequence) < self.num_processes:
            found = False
            
            for i in range(self.num_processes):
                if not finish[i]:
                    # Check if process can be satisfied
                    can_allocate = True
                    for j in range(self.num_resources):
                        if self.need[i][j] > work[j]:
                            can_allocate = False
                            break
                    
                    if can_allocate:
                        # Process can complete, add its resources to work
                        for j in range(self.num_resources):
                            work[j] += self.allocation[i][j]
                        finish[i] = True
                        safe_sequence.append(i)
                        found = True
                        break
            
            if not found:
                return False, []  # Unsafe state
        
        return True, safe_sequence
    
    def request_resources(self, process_id, request):
        """Check if resource request can be granted safely"""
        # Check if request is valid
        for i in range(self.num_resources):
            if request[i] > self.need[process_id][i]:
                return False, "Request exceeds maximum need"
            if request[i] > self.available[i]:
                return False, "Request exceeds available resources"
        
        # Temporarily allocate resources
        for i in range(self.num_resources):
            self.available[i] -= request[i]
            self.allocation[process_id][i] += request[i]
            self.need[process_id][i] -= request[i]
        
        # Check if state is safe
        is_safe, sequence = self.is_safe_state()
        
        if is_safe:
            return True, f"Request granted. Safe sequence: {sequence}"
        else:
            # Rollback allocation
            for i in range(self.num_resources):
                self.available[i] += request[i]
                self.allocation[process_id][i] -= request[i]
                self.need[process_id][i] += request[i]
            return False, "Request would lead to unsafe state"

# Example usage
allocation = [
    [0, 1, 0],  # Process 0
    [2, 0, 0],  # Process 1
    [3, 0, 2],  # Process 2
    [2, 1, 1],  # Process 3
    [0, 0, 2]   # Process 4
]

max_need = [
    [7, 5, 3],  # Process 0
    [3, 2, 2],  # Process 1
    [9, 0, 2],  # Process 2
    [2, 2, 2],  # Process 3
    [4, 3, 3]   # Process 4
]

available = [3, 3, 2]

banker = BankersAlgorithm(allocation, max_need, available)
is_safe, sequence = banker.is_safe_state()
print(f"Initial state is safe: {is_safe}")
if is_safe:
    print(f"Safe sequence: {sequence}")

# Test resource request
success, message = banker.request_resources(1, [1, 0, 2])
print(f"Request result: {message}")
```

---

## Common Interview Questions

### 11. Key OS Interview Questions

**Q: What is the difference between process and thread?**
**A:** See section 3 above.

**Q: Explain virtual memory and its benefits.**
**A:** Virtual memory allows programs to use more memory than physically available by using disk storage as extension of RAM. Benefits include:
- Memory isolation between processes
- Efficient memory utilization
- Support for larger programs
- Memory protection

**Q: What is thrashing in OS?**
**A:** Thrashing occurs when system spends more time swapping pages than executing processes. Happens when:
- Too many processes in memory
- Insufficient physical memory
- Poor page replacement algorithm

**Q: Explain different types of system calls.**
**A:**
- **Process control**: fork(), exec(), wait(), exit()
- **File management**: open(), read(), write(), close()
- **Device management**: ioctl(), read(), write()
- **Information maintenance**: getpid(), alarm(), sleep()
- **Communication**: pipe(), shmget(), mmap()

### Key Interview Tips:
1. **Understand fundamental concepts** before diving into details
2. **Use real-world analogies** to explain complex concepts
3. **Know the trade-offs** of different algorithms and approaches
4. **Practice implementing** basic algorithms (scheduling, page replacement)
5. **Understand modern OS features** (multi-core, virtualization)
6. **Be familiar with UNIX/Linux commands** and system calls

### Practice Topics:
1. Implement different scheduling algorithms
2. Simulate memory management techniques
3. Solve synchronization problems
4. Understand file system operations
5. Learn about modern OS features (containers, virtualization)

Remember: **Operating Systems concepts are fundamental to understanding how computers work - focus on the 'why' behind each concept!**
