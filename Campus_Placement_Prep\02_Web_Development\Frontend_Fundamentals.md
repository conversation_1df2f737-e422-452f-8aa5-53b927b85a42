# Frontend Fundamentals: HTML, CSS, JavaScript Interview Guide

## Table of Contents
1. [HTML Fundamentals](#html-fundamentals)
2. [CSS Concepts](#css-concepts)
3. [JavaScript Essentials](#javascript-essentials)
4. [DOM Manipulation](#dom-manipulation)
5. [Responsive Design](#responsive-design)
6. [Common Interview Questions](#common-interview-questions)

---

## HTML Fundamentals

### 1. What is HTML and its structure?

**Answer:**
HTML (HyperText Markup Language) is the standard markup language for creating web pages.

**Basic Structure:**
```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Page Title</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <header>
        <nav>Navigation</nav>
    </header>
    <main>
        <section>Content</section>
    </main>
    <footer>Footer</footer>
    <script src="script.js"></script>
</body>
</html>
```

**Real-world analogy:** HTML is like the skeleton of a house - it provides the basic structure and framework.

### 2. Semantic HTML Elements

**Answer:**
Semantic elements clearly describe their meaning to both browser and developer.

```html
<!-- Semantic HTML -->
<article>
    <header>
        <h1>Article Title</h1>
        <time datetime="2024-01-15">January 15, 2024</time>
    </header>
    <section>
        <p>Article content...</p>
    </section>
    <aside>
        <h3>Related Links</h3>
        <nav>
            <ul>
                <li><a href="#">Link 1</a></li>
                <li><a href="#">Link 2</a></li>
            </ul>
        </nav>
    </aside>
    <footer>
        <p>Author: John Doe</p>
    </footer>
</article>

<!-- Non-semantic (avoid) -->
<div class="article">
    <div class="header">
        <div class="title">Article Title</div>
    </div>
</div>
```

**Benefits:**
- Better SEO
- Improved accessibility
- Cleaner code structure
- Better browser support

### 3. HTML Forms and Input Types

```html
<form action="/submit" method="POST" novalidate>
    <!-- Text inputs -->
    <input type="text" name="username" required placeholder="Username">
    <input type="email" name="email" required placeholder="Email">
    <input type="password" name="password" required minlength="8">
    
    <!-- Number and date inputs -->
    <input type="number" name="age" min="18" max="100">
    <input type="date" name="birthdate">
    <input type="range" name="rating" min="1" max="10" value="5">
    
    <!-- Selection inputs -->
    <select name="country" required>
        <option value="">Select Country</option>
        <option value="us">United States</option>
        <option value="uk">United Kingdom</option>
    </select>
    
    <!-- Radio buttons -->
    <input type="radio" id="male" name="gender" value="male">
    <label for="male">Male</label>
    <input type="radio" id="female" name="gender" value="female">
    <label for="female">Female</label>
    
    <!-- Checkboxes -->
    <input type="checkbox" id="newsletter" name="newsletter" value="yes">
    <label for="newsletter">Subscribe to newsletter</label>
    
    <!-- Textarea -->
    <textarea name="message" rows="4" cols="50" placeholder="Your message"></textarea>
    
    <!-- File upload -->
    <input type="file" name="avatar" accept="image/*">
    
    <!-- Submit button -->
    <button type="submit">Submit</button>
</form>
```

---

## CSS Concepts

### 4. CSS Box Model

**Answer:**
The CSS box model describes how elements are rendered with content, padding, border, and margin.

```css
.box {
    width: 200px;           /* Content width */
    height: 100px;          /* Content height */
    padding: 20px;          /* Space inside border */
    border: 5px solid blue; /* Border around padding */
    margin: 10px;           /* Space outside border */
    
    /* Box-sizing property */
    box-sizing: border-box; /* Width includes padding and border */
}

/* Visual representation:
┌─────────────────────────────────────┐ ← margin
│ ┌─────────────────────────────────┐ │ ← border
│ │ ┌─────────────────────────────┐ │ │ ← padding
│ │ │        CONTENT              │ │ │
│ │ └─────────────────────────────┘ │ │
│ └─────────────────────────────────┘ │
└─────────────────────────────────────┘
*/
```

**Real-world analogy:** Think of a picture frame - content is the photo, padding is the mat, border is the frame, margin is the space between frames on a wall.

### 5. CSS Flexbox

```css
/* Flex Container */
.container {
    display: flex;
    flex-direction: row;        /* row | column | row-reverse | column-reverse */
    justify-content: center;    /* flex-start | flex-end | center | space-between | space-around | space-evenly */
    align-items: center;        /* flex-start | flex-end | center | stretch | baseline */
    flex-wrap: wrap;           /* nowrap | wrap | wrap-reverse */
    gap: 20px;                 /* Space between items */
}

/* Flex Items */
.item {
    flex: 1;                   /* flex-grow: 1, flex-shrink: 1, flex-basis: 0% */
    flex-grow: 1;              /* How much item should grow */
    flex-shrink: 0;            /* How much item should shrink */
    flex-basis: 200px;         /* Initial size before free space distribution */
    align-self: flex-end;      /* Override container's align-items */
}

/* Common Flexbox Patterns */
.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
}

.card-grid .card {
    flex: 1 1 300px; /* grow, shrink, basis */
}
```

### 6. CSS Grid

```css
/* Grid Container */
.grid-container {
    display: grid;
    grid-template-columns: 1fr 2fr 1fr;    /* 3 columns with ratios */
    grid-template-rows: auto 1fr auto;     /* 3 rows */
    grid-gap: 20px;                        /* Gap between grid items */
    height: 100vh;
    
    /* Named grid areas */
    grid-template-areas: 
        "header header header"
        "sidebar main aside"
        "footer footer footer";
}

/* Grid Items */
.header { grid-area: header; }
.sidebar { grid-area: sidebar; }
.main { grid-area: main; }
.aside { grid-area: aside; }
.footer { grid-area: footer; }

/* Alternative grid item positioning */
.item1 {
    grid-column: 1 / 3;    /* Start at column 1, end at column 3 */
    grid-row: 1 / 2;       /* Start at row 1, end at row 2 */
}

.item2 {
    grid-column: span 2;   /* Span 2 columns */
    grid-row: span 1;      /* Span 1 row */
}
```

---

## JavaScript Essentials

### 7. JavaScript Data Types and Variables

```javascript
// Primitive types
let name = "John";              // string
let age = 25;                   // number
let isActive = true;            // boolean
let data = null;                // null
let value;                      // undefined
let id = Symbol('id');          // symbol
let bigNumber = 123n;           // bigint

// Reference types
let person = {                  // object
    name: "John",
    age: 25
};
let numbers = [1, 2, 3, 4, 5];  // array
let greet = function() {        // function
    console.log("Hello!");
};

// Variable declarations
var oldWay = "function-scoped";     // Avoid using var
let modernWay = "block-scoped";     // Use let for variables
const constant = "immutable";       // Use const for constants

// Type checking
console.log(typeof name);           // "string"
console.log(Array.isArray(numbers)); // true
console.log(person instanceof Object); // true
```

### 8. Functions and Scope

```javascript
// Function declarations
function regularFunction(a, b) {
    return a + b;
}

// Function expressions
const functionExpression = function(a, b) {
    return a + b;
};

// Arrow functions
const arrowFunction = (a, b) => a + b;
const singleParam = x => x * 2;
const noParams = () => "Hello!";

// Scope examples
let globalVar = "I'm global";

function outerFunction() {
    let outerVar = "I'm in outer function";
    
    function innerFunction() {
        let innerVar = "I'm in inner function";
        console.log(globalVar);  // Accessible
        console.log(outerVar);   // Accessible
        console.log(innerVar);   // Accessible
    }
    
    innerFunction();
    // console.log(innerVar);   // Error: not accessible
}

// Closures
function createCounter() {
    let count = 0;
    return function() {
        count++;
        return count;
    };
}

const counter = createCounter();
console.log(counter()); // 1
console.log(counter()); // 2
```

### 9. Objects and Arrays

```javascript
// Object creation and manipulation
const person = {
    name: "John",
    age: 30,
    city: "New York",
    
    // Method
    greet() {
        return `Hello, I'm ${this.name}`;
    }
};

// Accessing properties
console.log(person.name);        // Dot notation
console.log(person['age']);      // Bracket notation

// Adding/modifying properties
person.email = "<EMAIL>";
person.age = 31;

// Object destructuring
const { name, age } = person;
const { name: personName, age: personAge } = person; // Renaming

// Array methods
const numbers = [1, 2, 3, 4, 5];

// Iteration methods
numbers.forEach(num => console.log(num));
const doubled = numbers.map(num => num * 2);
const evens = numbers.filter(num => num % 2 === 0);
const sum = numbers.reduce((acc, num) => acc + num, 0);

// Array manipulation
numbers.push(6);           // Add to end
numbers.unshift(0);        // Add to beginning
numbers.pop();             // Remove from end
numbers.shift();           // Remove from beginning

// Array destructuring
const [first, second, ...rest] = numbers;
```

### 10. Promises and Async/Await

```javascript
// Promise creation
const fetchData = () => {
    return new Promise((resolve, reject) => {
        setTimeout(() => {
            const success = Math.random() > 0.5;
            if (success) {
                resolve({ data: "Success!" });
            } else {
                reject(new Error("Failed to fetch data"));
            }
        }, 1000);
    });
};

// Promise usage
fetchData()
    .then(result => console.log(result))
    .catch(error => console.error(error))
    .finally(() => console.log("Operation completed"));

// Async/Await
async function getData() {
    try {
        const result = await fetchData();
        console.log(result);
        return result;
    } catch (error) {
        console.error("Error:", error.message);
        throw error;
    }
}

// Multiple async operations
async function fetchMultipleData() {
    try {
        // Sequential
        const data1 = await fetchData();
        const data2 = await fetchData();
        
        // Parallel
        const [result1, result2] = await Promise.all([
            fetchData(),
            fetchData()
        ]);
        
        return { result1, result2 };
    } catch (error) {
        console.error("One or more requests failed:", error);
    }
}
```

---

## DOM Manipulation

### 11. Selecting and Modifying Elements

```javascript
// Element selection
const element = document.getElementById('myId');
const elements = document.getElementsByClassName('myClass');
const tagElements = document.getElementsByTagName('div');
const querySelector = document.querySelector('.my-class');
const querySelectorAll = document.querySelectorAll('.my-class');

// Content manipulation
element.textContent = "New text content";
element.innerHTML = "<strong>Bold text</strong>";
element.innerText = "Visible text only";

// Attribute manipulation
element.setAttribute('data-value', '123');
element.getAttribute('data-value');
element.removeAttribute('data-value');
element.hasAttribute('data-value');

// Class manipulation
element.classList.add('new-class');
element.classList.remove('old-class');
element.classList.toggle('active');
element.classList.contains('my-class');

// Style manipulation
element.style.color = 'red';
element.style.backgroundColor = 'blue';
element.style.display = 'none';

// Creating and inserting elements
const newElement = document.createElement('div');
newElement.textContent = 'New element';
newElement.className = 'new-element';

document.body.appendChild(newElement);
element.insertBefore(newElement, element.firstChild);
element.insertAdjacentHTML('beforeend', '<p>New paragraph</p>');
```

### 12. Event Handling

```javascript
// Event listeners
const button = document.querySelector('#myButton');

// Basic event listener
button.addEventListener('click', function(event) {
    console.log('Button clicked!');
    event.preventDefault(); // Prevent default behavior
    event.stopPropagation(); // Stop event bubbling
});

// Arrow function event listener
button.addEventListener('click', (e) => {
    console.log('Clicked at:', e.clientX, e.clientY);
});

// Event delegation (for dynamic content)
document.addEventListener('click', function(e) {
    if (e.target.classList.contains('dynamic-button')) {
        console.log('Dynamic button clicked!');
    }
});

// Form events
const form = document.querySelector('#myForm');
form.addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(form);
    const data = Object.fromEntries(formData);
    console.log('Form data:', data);
});

// Input events
const input = document.querySelector('#myInput');
input.addEventListener('input', function(e) {
    console.log('Input value:', e.target.value);
});

// Keyboard events
document.addEventListener('keydown', function(e) {
    if (e.key === 'Enter') {
        console.log('Enter key pressed');
    }
    if (e.ctrlKey && e.key === 's') {
        e.preventDefault();
        console.log('Ctrl+S pressed');
    }
});
```

---

## Responsive Design

### 13. Media Queries and Responsive Layouts

```css
/* Mobile-first approach */
.container {
    width: 100%;
    padding: 10px;
}

/* Tablet styles */
@media screen and (min-width: 768px) {
    .container {
        max-width: 750px;
        margin: 0 auto;
        padding: 20px;
    }
}

/* Desktop styles */
@media screen and (min-width: 1024px) {
    .container {
        max-width: 1200px;
        padding: 30px;
    }
}

/* High-resolution displays */
@media screen and (min-resolution: 2dppx) {
    .logo {
        background-image: url('<EMAIL>');
        background-size: 100px 50px;
    }
}

/* Responsive grid */
.grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 20px;
}

@media (min-width: 768px) {
    .grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1024px) {
    .grid {
        grid-template-columns: repeat(3, 1fr);
    }
}
```

---

## Common Interview Questions

### 14. What is the difference between `==` and `===` in JavaScript?

**Answer:**
- `==` performs type coercion (loose equality)
- `===` checks both value and type (strict equality)

```javascript
// Loose equality (==)
console.log(5 == "5");     // true (string "5" converted to number)
console.log(true == 1);    // true (boolean converted to number)
console.log(null == undefined); // true (special case)

// Strict equality (===)
console.log(5 === "5");    // false (different types)
console.log(true === 1);   // false (different types)
console.log(null === undefined); // false (different types)

// Best practice: Always use === unless you specifically need type coercion
```

### 15. Explain Event Bubbling and Capturing

**Answer:**
Event propagation has three phases: capturing, target, and bubbling.

```html
<div id="outer">
    <div id="inner">
        <button id="button">Click me</button>
    </div>
</div>
```

```javascript
// Event bubbling (default)
document.getElementById('outer').addEventListener('click', () => {
    console.log('Outer div clicked');
});

document.getElementById('inner').addEventListener('click', () => {
    console.log('Inner div clicked');
});

document.getElementById('button').addEventListener('click', () => {
    console.log('Button clicked');
});

// When button is clicked, output:
// "Button clicked"
// "Inner div clicked"
// "Outer div clicked"

// Event capturing (useCapture = true)
document.getElementById('outer').addEventListener('click', () => {
    console.log('Outer div clicked (capturing)');
}, true);

// Stop propagation
document.getElementById('button').addEventListener('click', (e) => {
    e.stopPropagation(); // Prevents bubbling
    console.log('Button clicked');
});
```

### Key Interview Tips:
1. **Understand the fundamentals** before moving to frameworks
2. **Practice DOM manipulation** without libraries
3. **Know CSS layout techniques** (Flexbox, Grid)
4. **Understand asynchronous JavaScript** (Promises, async/await)
5. **Be familiar with responsive design principles**
6. **Know browser compatibility issues** and solutions

### Practice Projects:
1. Build a responsive portfolio website
2. Create a todo list with local storage
3. Implement a image carousel/slider
4. Build a form with validation
5. Create a simple calculator

Remember: **Master the fundamentals first - frameworks come and go, but core web technologies remain essential!**
