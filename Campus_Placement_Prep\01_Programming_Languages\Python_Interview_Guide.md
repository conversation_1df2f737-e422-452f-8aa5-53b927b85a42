# Python Interview Questions & Concepts Guide

## Table of Contents
1. [Python Fundamentals](#python-fundamentals)
2. [Data Structures](#data-structures)
3. [Object-Oriented Programming](#object-oriented-programming)
4. [Advanced Python Features](#advanced-python-features)
5. [Common Interview Questions](#common-interview-questions)
6. [Real-World Problem Solving](#real-world-problem-solving)

---

## Python Fundamentals

### 1. What makes Python special?

**Answer:**
- **Interpreted language** - No compilation step needed
- **Dynamic typing** - Variables don't need type declarations
- **High-level language** - Abstracts complex details
- **Extensive libraries** - Rich ecosystem of packages
- **Readable syntax** - English-like code structure

**Real-world analogy:** Python is like a Swiss Army knife - versatile, easy to use, and has tools for almost every task.

```python
# Python's simplicity
numbers = [1, 2, 3, 4, 5]
squares = [x**2 for x in numbers]  # List comprehension
print(squares)  # [1, 4, 9, 16, 25]
```

### 2. Mutable vs Immutable Objects

**Immutable:** int, float, string, tuple, frozenset
**Mutable:** list, dict, set, user-defined classes

```python
# Immutable example
a = "hello"
b = a
a += " world"
print(a)  # "hello world"
print(b)  # "hello" (unchanged)

# Mutable example
list1 = [1, 2, 3]
list2 = list1
list1.append(4)
print(list1)  # [1, 2, 3, 4]
print(list2)  # [1, 2, 3, 4] (changed!)
```

**Real-world analogy:** Immutable objects are like printed books (can't change content), mutable objects are like whiteboards (can modify content).

### 3. Python Memory Management

**Answer:**
- **Reference counting** - Tracks how many variables reference an object
- **Garbage collection** - Automatically frees unused memory
- **Memory pools** - Optimizes allocation for small objects

```python
import sys

a = [1, 2, 3]
print(sys.getrefcount(a))  # Reference count

# When reference count reaches 0, object is deleted
a = None  # Original list becomes eligible for garbage collection
```

---

## Data Structures

### 4. List vs Tuple vs Set vs Dictionary

| Feature | List | Tuple | Set | Dictionary |
|---------|------|-------|-----|------------|
| Mutable | Yes | No | Yes | Yes |
| Ordered | Yes | Yes | No* | Yes** |
| Duplicates | Yes | Yes | No | Keys: No, Values: Yes |
| Indexing | Yes | Yes | No | By key |

*Python 3.7+ maintains insertion order for sets
**Python 3.7+ maintains insertion order for dictionaries

```python
# List - ordered, mutable, allows duplicates
fruits = ['apple', 'banana', 'apple']
fruits.append('orange')

# Tuple - ordered, immutable, allows duplicates
coordinates = (10, 20)
# coordinates.append(30)  # Error! Tuples are immutable

# Set - unordered, mutable, no duplicates
unique_fruits = {'apple', 'banana', 'apple'}  # {'apple', 'banana'}

# Dictionary - key-value pairs
student = {'name': 'John', 'age': 20, 'grade': 'A'}
```

### 5. List Comprehensions and Generator Expressions

**List Comprehensions:**
```python
# Traditional approach
squares = []
for x in range(10):
    if x % 2 == 0:
        squares.append(x**2)

# List comprehension
squares = [x**2 for x in range(10) if x % 2 == 0]

# Nested comprehension
matrix = [[i*j for j in range(3)] for i in range(3)]
```

**Generator Expressions:**
```python
# Memory efficient for large datasets
squares_gen = (x**2 for x in range(1000000))  # Doesn't create all values immediately

# Use when you need to iterate once
for square in squares_gen:
    if square > 100:
        break
```

**Real-world analogy:** List comprehension is like preparing all meals at once, generator is like cooking meals as needed.

---

## Object-Oriented Programming

### 6. Classes and Objects

```python
class Car:
    # Class variable (shared by all instances)
    wheels = 4
    
    def __init__(self, brand, model):
        # Instance variables (unique to each instance)
        self.brand = brand
        self.model = model
        self.speed = 0
    
    def accelerate(self, increment):
        self.speed += increment
        return f"{self.brand} {self.model} is now going {self.speed} mph"
    
    def __str__(self):
        return f"{self.brand} {self.model}"
    
    def __repr__(self):
        return f"Car('{self.brand}', '{self.model}')"

# Usage
my_car = Car("Toyota", "Camry")
print(my_car.accelerate(30))  # Toyota Camry is now going 30 mph
```

### 7. Inheritance and Polymorphism

```python
class Animal:
    def __init__(self, name):
        self.name = name
    
    def make_sound(self):
        pass  # Abstract method
    
    def introduce(self):
        return f"I'm {self.name}"

class Dog(Animal):
    def make_sound(self):
        return "Woof!"
    
    def fetch(self):
        return f"{self.name} is fetching the ball"

class Cat(Animal):
    def make_sound(self):
        return "Meow!"
    
    def climb(self):
        return f"{self.name} is climbing a tree"

# Polymorphism in action
animals = [Dog("Buddy"), Cat("Whiskers")]
for animal in animals:
    print(f"{animal.introduce()}: {animal.make_sound()}")
```

### 8. Special Methods (Magic Methods)

```python
class Vector:
    def __init__(self, x, y):
        self.x = x
        self.y = y
    
    def __add__(self, other):
        return Vector(self.x + other.x, self.y + other.y)
    
    def __str__(self):
        return f"Vector({self.x}, {self.y})"
    
    def __len__(self):
        return int((self.x**2 + self.y**2)**0.5)
    
    def __eq__(self, other):
        return self.x == other.x and self.y == other.y

# Usage
v1 = Vector(1, 2)
v2 = Vector(3, 4)
v3 = v1 + v2  # Calls __add__
print(v3)     # Calls __str__
print(len(v1)) # Calls __len__
```

---

## Advanced Python Features

### 9. Decorators

**Answer:**
Decorators modify or enhance functions without changing their code.

```python
import time
from functools import wraps

def timer(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        start = time.time()
        result = func(*args, **kwargs)
        end = time.time()
        print(f"{func.__name__} took {end - start:.4f} seconds")
        return result
    return wrapper

@timer
def slow_function():
    time.sleep(1)
    return "Done!"

# Usage
result = slow_function()  # Automatically times the function
```

**Real-world analogy:** A decorator is like gift wrapping - it adds functionality (beauty) without changing the gift (original function).

### 10. Context Managers

```python
# Built-in context manager
with open('file.txt', 'r') as f:
    content = f.read()
# File automatically closed, even if exception occurs

# Custom context manager
class DatabaseConnection:
    def __enter__(self):
        print("Connecting to database...")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        print("Closing database connection...")
        if exc_type:
            print(f"Exception occurred: {exc_val}")
        return False  # Don't suppress exceptions

# Usage
with DatabaseConnection() as db:
    print("Performing database operations...")
    # Connection automatically closed
```

### 11. Generators and Iterators

```python
# Generator function
def fibonacci(n):
    a, b = 0, 1
    for _ in range(n):
        yield a
        a, b = b, a + b

# Usage
fib_gen = fibonacci(10)
for num in fib_gen:
    print(num, end=' ')  # 0 1 1 2 3 5 8 13 21 34

# Custom iterator
class CountDown:
    def __init__(self, start):
        self.start = start
    
    def __iter__(self):
        return self
    
    def __next__(self):
        if self.start <= 0:
            raise StopIteration
        self.start -= 1
        return self.start + 1

# Usage
for num in CountDown(5):
    print(num)  # 5 4 3 2 1
```

---

## Common Interview Questions

### 12. What is the difference between `is` and `==`?

**Answer:**
- `==` compares **values** (equality)
- `is` compares **identity** (same object in memory)

```python
a = [1, 2, 3]
b = [1, 2, 3]
c = a

print(a == b)  # True (same values)
print(a is b)  # False (different objects)
print(a is c)  # True (same object)

# Special case with small integers and strings
x = 5
y = 5
print(x is y)  # True (Python caches small integers)

x = 1000
y = 1000
print(x is y)  # False (larger integers not cached)
```

### 13. Explain Python's GIL (Global Interpreter Lock)

**Answer:**
GIL is a mutex that protects access to Python objects, preventing multiple threads from executing Python bytecode simultaneously.

**Impact:**
- **CPU-bound tasks:** GIL limits performance (use multiprocessing)
- **I/O-bound tasks:** GIL has minimal impact (threads work well)

```python
import threading
import time

# CPU-bound task (limited by GIL)
def cpu_bound_task():
    count = 0
    for i in range(10000000):
        count += 1
    return count

# I/O-bound task (not limited by GIL)
def io_bound_task():
    time.sleep(1)  # Simulates I/O operation
    return "Done"

# For CPU-bound: use multiprocessing
from multiprocessing import Pool

# For I/O-bound: threading works fine
from concurrent.futures import ThreadPoolExecutor
```

### 14. Lambda Functions and Higher-Order Functions

```python
# Lambda functions
square = lambda x: x**2
print(square(5))  # 25

# Higher-order functions
numbers = [1, 2, 3, 4, 5]

# map() - applies function to each element
squares = list(map(lambda x: x**2, numbers))

# filter() - filters elements based on condition
evens = list(filter(lambda x: x % 2 == 0, numbers))

# reduce() - reduces sequence to single value
from functools import reduce
sum_all = reduce(lambda x, y: x + y, numbers)

# sorted() with custom key
students = [('Alice', 85), ('Bob', 90), ('Charlie', 78)]
sorted_by_grade = sorted(students, key=lambda x: x[1], reverse=True)
```

---

## Real-World Problem Solving

### 15. Implement a LRU Cache

```python
from collections import OrderedDict

class LRUCache:
    def __init__(self, capacity):
        self.capacity = capacity
        self.cache = OrderedDict()
    
    def get(self, key):
        if key in self.cache:
            # Move to end (most recently used)
            self.cache.move_to_end(key)
            return self.cache[key]
        return -1
    
    def put(self, key, value):
        if key in self.cache:
            # Update existing key
            self.cache.move_to_end(key)
        elif len(self.cache) >= self.capacity:
            # Remove least recently used (first item)
            self.cache.popitem(last=False)
        
        self.cache[key] = value

# Usage
cache = LRUCache(2)
cache.put(1, 'one')
cache.put(2, 'two')
print(cache.get(1))  # 'one'
cache.put(3, 'three')  # Removes key 2
print(cache.get(2))  # -1 (not found)
```

### 16. Design a Simple Web Scraper

```python
import requests
from bs4 import BeautifulSoup
import csv

class WebScraper:
    def __init__(self, base_url):
        self.base_url = base_url
        self.session = requests.Session()
    
    def scrape_page(self, url):
        try:
            response = self.session.get(url)
            response.raise_for_status()
            return BeautifulSoup(response.content, 'html.parser')
        except requests.RequestException as e:
            print(f"Error scraping {url}: {e}")
            return None
    
    def extract_data(self, soup, selectors):
        data = {}
        for key, selector in selectors.items():
            elements = soup.select(selector)
            data[key] = [elem.get_text().strip() for elem in elements]
        return data
    
    def save_to_csv(self, data, filename):
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            if data:
                writer = csv.DictWriter(csvfile, fieldnames=data[0].keys())
                writer.writeheader()
                writer.writerows(data)

# Usage example
scraper = WebScraper("https://example.com")
soup = scraper.scrape_page("https://example.com/products")
if soup:
    selectors = {
        'titles': '.product-title',
        'prices': '.product-price'
    }
    data = scraper.extract_data(soup, selectors)
    print(data)
```

### Key Interview Tips:
1. **Explain Pythonic ways** of solving problems
2. **Discuss time and space complexity**
3. **Mention relevant libraries** (NumPy, Pandas, etc.)
4. **Consider edge cases** and error handling
5. **Show knowledge of Python best practices** (PEP 8, etc.)

### Practice Problems:
1. Implement a decorator for caching function results
2. Create a context manager for database transactions
3. Build a simple REST API using Flask
4. Design a data processing pipeline with generators
5. Implement a thread-safe counter class

### Python Best Practices:
- Follow PEP 8 style guide
- Use meaningful variable names
- Write docstrings for functions and classes
- Handle exceptions appropriately
- Use list comprehensions when appropriate
- Prefer built-in functions and libraries

Remember: **Python emphasizes readability and simplicity - write code that others can easily understand!**
